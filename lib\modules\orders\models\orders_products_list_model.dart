import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';

class OrdersProductsListModel {
  int? productId;
  int? productDunId;
  String? name;
  String? productCode;
  String? productCodeLabel;
  double? price;
  double? discount;
  double? discountChange;
  double? discountPercent;
  double? discountMax;
  double? discountMin;
  double? priceOrder;
  double? totalOrder;
  String? photo;
  bool? isActive;
  int? qtdy;
  int? qtdyReal;
  int? minQtdy;
  int? maxQtdy;
  bool? emphasis;
  List<OrdersProductsDiscountRangeModel>? discountRange;
  OrdersProductsDiscountRangeModel? discountRangeCurrent;
  List<String>? filterIds;
  DateTime? limitDate;
  ProductStateEnum? states;
  String? status;
  int? distributorId;
  double? discountRep;
  double? discountManager;
  double? discountApply;
  bool? priceDistributor;
  int? stock;
  double? discountBase;
  double? discountNegotiated;
  double? discountNegotiation;
  double? discountTotal;
  double? discountAdditional;
  TextEditingController? valueController;
  TextEditingController? discountChangeController;
  bool? isEditDiscount;
  TextEditingController? qtdyController;
  FocusNode? focusNode;
  FocusNode? focusNodeDiscountChange;
  MetricaMdtr? metricaMdtr;
  bool? destaque;
  int? paymentTermId;
  double? taxSubstitution;
  bool? showPriceWithSpecialST;
  bool? showPriceWithRepST;
  double? discountTaxSubstitution;
  OrdersProductsListModel({
    this.productId,
    this.destaque,
    this.productDunId,
    this.name,
    this.productCode,
    this.productCodeLabel,
    this.price,
    this.discount,
    this.discountChange,
    this.discountPercent,
    this.discountMax,
    this.discountMin,
    this.priceOrder,
    this.totalOrder,
    this.photo,
    this.status,
    this.isActive,
    this.qtdy,
    this.qtdyReal,
    this.qtdyController,
    this.focusNode,
    this.focusNodeDiscountChange,
    this.minQtdy,
    this.maxQtdy,
    this.emphasis,
    this.discountRange,
    this.discountRangeCurrent,
    this.filterIds,
    this.limitDate,
    this.states,
    this.distributorId,
    this.discountApply,
    this.discountBase,
    this.discountManager,
    this.discountNegotiated,
    this.discountNegotiation,
    this.discountRep,
    this.discountTotal,
    this.discountAdditional,
    this.priceDistributor,
    this.stock,
    this.valueController,
    this.discountChangeController,
    this.isEditDiscount,
    this.metricaMdtr,
    this.paymentTermId,
    this.taxSubstitution,
    this.showPriceWithSpecialST,
    this.showPriceWithRepST,
    this.discountTaxSubstitution,
  });

  factory OrdersProductsListModel.fromJson(Map<String, dynamic> json) {
    return OrdersProductsListModel(
      productId: json['productId'],
      productDunId: json['productDunId'],
      name: json['name'],
      productCode: json['productCode'],
      productCodeLabel: json['productCodeLabel'],
      price: json['price']?.toDouble(),
      discount: json['discount']?.toDouble(),
      discountChange: json['discountChange']?.toDouble(),
      discountPercent: json['discountPercent']?.toDouble(),
      discountMax: json['discountMax']?.toDouble(),
      discountMin: json['discountMin']?.toDouble(),
      priceOrder: json['priceOrder']?.toDouble(),
      totalOrder: json['totalOrder']?.toDouble(),
      photo: json['photo'],
      isActive: json['isActive'],
      qtdy: json['qtdy'],
      qtdyReal: json['qtdyReal'],
      minQtdy: json['minQtdy'],
      maxQtdy: json['maxQtdy'],
      emphasis: json['emphasis'],
      filterIds: List<String>.from(json['filterIds'] ?? []),
      limitDate:
          json['limitDate'] == null ? null : DateTime.parse(json['limitDate']),
      states: json['states'] == null
          ? null
          : ProductStateEnum.values
              .firstWhere((e) => e.toString() == json['states']),
      status: json['status'],
      distributorId: json['distributorId'],
      discountRep: json['discountRep']?.toDouble(),
      discountManager: json['discountManager']?.toDouble(),
      discountApply: json['discountApply']?.toDouble(),
      priceDistributor: json['priceDistributor'],
      stock: json['stock'],
      discountBase: json['discountBase']?.toDouble(),
      discountNegotiated: json['discountNegotiated']?.toDouble(),
      discountNegotiation: json['discountNegotiation']?.toDouble(),
      discountTotal: json['discountTotal']?.toDouble(),
      discountAdditional: json['discountAdditional']?.toDouble(),
      destaque: json['destaque'],
      paymentTermId: json['paymentTermId'],
      discountRange: (json['discountRange'] as List)
          .map((item) => OrdersProductsDiscountRangeModel.fromJson(item))
          .toList(),
      discountRangeCurrent: json['discountRangeCurrent'] != null
          ? OrdersProductsDiscountRangeModel.fromJson(
              json['discountRangeCurrent'])
          : null,
      taxSubstitution: json['taxSubstitution']?.toDouble(),
      showPriceWithSpecialST: json['showPriceWithSpecialST'],
      showPriceWithRepST: json['showPriceWithRepST'],
      discountTaxSubstitution: json['discountTaxSubstitution']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productDunId': productDunId,
      'name': name,
      'productCode': productCode,
      'productCodeLabel': productCodeLabel,
      'price': price,
      'discount': discount,
      'discountChange': discountChange,
      'discountPercent': discountPercent,
      'discountMax': discountMax,
      'discountMin': discountMin,
      'priceOrder': priceOrder,
      'totalOrder': totalOrder,
      'photo': photo,
      'isActive': isActive,
      'qtdy': qtdy,
      'qtdyReal': qtdyReal,
      'minQtdy': minQtdy,
      'maxQtdy': maxQtdy,
      'emphasis': emphasis,
      'filterIds': filterIds,
      'limitDate': limitDate?.toIso8601String(),
      'states': states?.toString(),
      'status': status,
      'distributorId': distributorId,
      'discountRep': discountRep,
      'discountManager': discountManager,
      'discountApply': discountApply,
      'priceDistributor': priceDistributor,
      'stock': stock,
      'discountBase': discountBase,
      'discountNegotiated': discountNegotiated,
      'discountNegotiation': discountNegotiation,
      'discountTotal': discountTotal,
      'discountAdditional': discountAdditional,
      'destaque': destaque,
      'paymentTermId': paymentTermId,
      'discountRange': discountRange?.map((range) => range.toJson()).toList(),
      'discountRangeCurrent': discountRangeCurrent?.toJson(),
      'taxSubstitution': taxSubstitution,
      'showPriceWithSpecialST': showPriceWithSpecialST,
      'showPriceWithRepST': showPriceWithRepST,
      'discountTaxSubstitution': discountTaxSubstitution,
    };
  }

  String getDiscountRange() {
    if (discountRange != null && discountRange!.isNotEmpty) {
      final result = discountRange!.map((range) {
        return "${range.minQtdy} a ${range.maxQtdy} = ${ordersController.calculateDiscountRange(range).formatPercentWithSymbol()}";
      }).join('\n');
      return result;
    }
    return "-";
  }

  List<String> getDiscountRangeList() {
    if (discountRange != null && discountRange!.isNotEmpty) {
      return discountRange!.map((range) {
        return "${range.minQtdy} a ${range.maxQtdy} = ${ordersController.calculateDiscountRange(range).formatPercentWithSymbol()}";
      }).toList();
    }
    return [];
  }
}

class OrdersProductsDiscountRangeModel {
  int? minQtdy;
  int? maxQtdy;
  double? baseDiscountPercentage;
  CondicaoComercialModel? comercialCondition;
  double? discountMin;
  double? discountMax;
  OrdersProductsDiscountRangeModel({
    this.minQtdy,
    this.maxQtdy,
    this.baseDiscountPercentage,
    this.comercialCondition,
    this.discountMin,
    this.discountMax,
  });

  factory OrdersProductsDiscountRangeModel.fromJson(Map<String, dynamic> json) {
    return OrdersProductsDiscountRangeModel(
      minQtdy: json['minQtdy'],
      maxQtdy: json['maxQtdy'],
      baseDiscountPercentage: json['baseDiscountPercentage']?.toDouble(),
      comercialCondition: json['comercialCondition'] != null
          ? CondicaoComercialModel.fromJson(json['comercialCondition'])
          : null,
      discountMin: json['discountMin']?.toDouble(),
      discountMax: json['discountMax']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'minQtdy': minQtdy,
      'maxQtdy': maxQtdy,
      'baseDiscountPercentage': baseDiscountPercentage,
      'comercialCondition': comercialCondition?.toJson(),
      'discountMin': discountMin,
      'discountMax': discountMax,
    };
  }
}

class DistinctDistributorPayment {
  final int? distributorId;
  final int? paymentTermId;

  DistinctDistributorPayment({this.distributorId, this.paymentTermId});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DistinctDistributorPayment &&
          runtimeType == other.runtimeType &&
          distributorId == other.distributorId &&
          paymentTermId == other.paymentTermId;

  @override
  int get hashCode => distributorId.hashCode ^ paymentTermId.hashCode;
}
