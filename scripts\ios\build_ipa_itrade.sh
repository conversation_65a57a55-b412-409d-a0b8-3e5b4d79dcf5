#!/bin/bash

# Caminhos
PROJECT_PATH="ios"
WORKSPACE_PATH="$PROJECT_PATH/Runner.xcworkspace"
SCHEME="itrade"
CONFIGURATION="Release"
ARCHIVE_PATH="$PROJECT_PATH/build/Runner.xcarchive"
EXPORT_OPTIONS_PLIST="$PROJECT_PATH/ExportOptions.plist"
EXPORT_PATH="$HOME/Downloads/MarketplacePlk/itrade"

# Limpar o diretório de exportação
echo "Limpando o diretório de exportação..."
rm -rf "$EXPORT_PATH"/*
rm -rf "$EXPORT_PATH"/.*

# Executar comandos Flutter e CocoaPods
echo "Executando flutter clean..."
flutter clean

echo "Executando flutter pub get..."
flutter pub get
x``
echo "Executando pod install..."
cd ios
pod install --repo-update
cd ..

# Limpar o diretório de construção
echo "Limpando o diretório de construção..."
xcodebuild clean -workspace "$WORKSPACE_PATH" -scheme "$SCHEME" -configuration "$CONFIGURATION"

# Arquivar o projeto
echo "Arquivando o projeto..."
xcodebuild archive -workspace "$WORKSPACE_PATH" -scheme "$SCHEME" -configuration "$CONFIGURATION" -archivePath "$ARCHIVE_PATH" -destination 'generic/platform=iOS'

# Verificar se o arquivo de arquivamento foi gerado
if [ ! -d "$ARCHIVE_PATH" ]; then
    echo "Erro: Arquivamento falhou, o diretório $ARCHIVE_PATH não foi encontrado."
    exit 1
fi

# Exportar o IPA
echo "Exportando o IPA..."
xcodebuild -exportArchive -archivePath "$ARCHIVE_PATH" -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" -exportPath "$EXPORT_PATH"

# Verificar se algum arquivo IPA foi gerado
IPA_FILE=$(find "$EXPORT_PATH" -name "*.ipa" | head -n 1)
if [ -z "$IPA_FILE" ]; then
    echo "Erro: Exportação falhou, nenhum arquivo IPA foi encontrado em $EXPORT_PATH."
    exit 1
fi

echo "Build and export complete. IPA is located at: $IPA_FILE"

# Apagar o diretório ios/build
echo "Apagando o diretório ios/build..."
rm -rf "$PROJECT_PATH/build"

echo "Pasta ios/build apagada com sucesso."
