import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/pages/widgets/footer_item.dart';
import 'package:pharmalink/modules/orders/pages/widgets/order_filter.dart';
import 'package:pharmalink/modules/orders/pages/widgets/order_filter_alphabetic.dart';
import 'package:pharmalink/modules/orders/pages/widgets/products_item.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  final ScrollController _scrollController = ScrollController();
  bool _showLabel = true;
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels > 150 && _showLabel == true) {
        setState(() {
          _showLabel = false;
        });
      } else if (_scrollController.position.pixels <= 150 &&
          _showLabel == false) {
        setState(() {
          _showLabel = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrdersController>(
      "Orders Page",
      initState: (state) async {
        Future.delayed(const Duration(milliseconds: 300), () async {
          if (Get.arguments == null || Get.arguments['edit'] == null) {
            await state.controller!.initialize();
          } else {
            await state.controller!.onEditOrder(Get.arguments['edit']);
          }
        });
      },
      builder: (ctrl) {
        return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LabelWidget(
                  title: ctrl.isEditOrder == true
                      ? ctrl.pdvName!
                      : globalParams.getCurrentStore()?.razaoSocial ?? "-",
                  fontSize: DeviceSize.fontSize(14, 18),
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
                LabelWidget(
                  title: ctrl.isEditOrder == true
                      ? ctrl.pdvCnpj!
                      : globalParams.getCurrentStore()?.cNPJ ?? "-",
                  fontSize: DeviceSize.fontSize(11, 13),
                  textColor: whiteColor,
                ),
              ],
            ),
            actions: [
              CustomInkWell(
                onTap: () async => await ctrl.advance(),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                  child: LabelWidget(
                    title: "Avançar".toUpperCase(),
                    fontSize: DeviceSize.fontSize(16, 19),
                    textColor: whiteColor,
                  ),
                ),
              ),
            ],
            leading: IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: whiteColor,
              ),
              onPressed: () {
                GetC.close();
              },
            ),
          ),
          body: Padding(
            padding:
                const EdgeInsets.only(left: 8, top: 8, right: 0, bottom: 8),
            child: Row(
              children: [
                Flexible(
                  flex: 9,
                  child: Container(
                      color: Colors.white,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_showLabel)
                            OrdersCardInfoWidget(
                              orderType: globalParams.getTypeOrderName(),
                              tabloidName: globalParams.order.tabloidName,
                              distributors: globalParams
                                  .order.currentDistributors
                                  ?.map((e) =>
                                      e.distribuidor?.razaoSocial ??
                                      e.distribuidor?.nomeFantasia ??
                                      "Sem identificação")
                                  .toList(),
                              paymentType:
                                  globalParams.order.deadlinePayment?.descricao,
                            ),

                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 4),
                            child: LabelWidget(
                              title: "Selecione um filtro",
                              textColor: Colors.grey,
                              fontSize: DeviceSize.fontSize(14, 18),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: OrdersFilterWidget(),
                          ),
                          const Gap(10),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: SearchTextFieldWidget(
                              label: "Pesquisa de produto",
                              hintText: 'Pesquise pelo nome ou EAN do produto',
                              borderColor: themesController.getIconColor(),
                              iconColor: themesController.getIconColor(),
                              iconSize: 18,
                              labelColor: Colors.grey,
                              labelFontSize: DeviceSize.fontSize(14, 18),
                              keyboardType: TextInputType.text,
                              controller: ctrl.searchController,
                              onChanged: ctrl.setSearchFilter,
                              trailingIcon: FontAwesomeIcons.xmark,
                              trailingTap: () {
                                ctrl.setSearchFilter(null);
                              },
                            ),
                          ),

                          20.toHeightSpace(),
                          //Agui eu preciso de um Listview
                          if (ctrl.productList.isNotEmpty)
                            Expanded(
                              child: ListView.builder(
                                controller: _scrollController,
                                itemCount: ctrl.productList.length,
                                itemBuilder: (context, index) {
                                  return OrdersProductsItem(
                                    data: ctrl.productList[index],
                                  );
                                },
                              ),
                            ),

                          if (ctrl.productList.isEmpty) ...[
                            50.toHeightSpace(),
                            Center(
                                child: Icon(
                              FontAwesomeIcons.opencart,
                              size: 48.w,
                            )),
                            10.toHeightSpace(),
                            Center(
                              child: LabelWidget(
                                title: "Nenhum produto encontrado!",
                                fontSize: DeviceSize.fontSize(14, 18),
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ]
                        ],
                      )),
                ),
                Flexible(
                  flex: 1,
                  child: Container(
                      color: Colors.white,
                      child: const OrdersFilterAlphabeticWidget()),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            color: themesController.getPrimaryColor(),
            height: DeviceSize.height(80, 90),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal, // Habilita a rolagem horizontal
              child: Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: DeviceSize.width(8, 16),
                    vertical: DeviceSize.height(10, 16)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: DeviceSize.width(85, 105),
                      child: OrderFooterItem(
                        title: AppStrings.orderFooterTotalApresentation,
                        value: ctrl.footer.totalApresentation!.toString(),
                      ),
                    ),
                    SizedBox(
                      width: DeviceSize.width(85, 105),
                      child: OrderFooterItem(
                        title: AppStrings.orderFooterTotalUnits,
                        value: ctrl.footer.totalUnits!.toString(),
                      ),
                    ),
                    if (globalParams.getTabloidSelected() != null)
                      SizedBox(
                        width: DeviceSize.width(85, 105),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterQtdyMinTotal,
                          color: ctrl.isItemMinRequired == true
                              ? Colors.red
                              : null,
                          value: globalParams
                                  .getTabloidSelected()
                                  ?.itemMinimumQuantity
                                  ?.toString() ??
                              "---",
                        ),
                      ),
                    if (globalParams.getTabloidSelected() != null)
                      SizedBox(
                        width: DeviceSize.width(85, 105),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterQtdyMaxTotal,
                          color: ctrl.isItemMaxRequired == true
                              ? Colors.red
                              : null,
                          value: globalParams
                                  .getTabloidSelected()
                                  ?.itemMaximumQuantity
                                  ?.toString() ??
                              "---",
                        ),
                      ),
                    SizedBox(
                      width: DeviceSize.width(85, 105),
                      child: OrderFooterItem(
                        title: AppStrings.orderFooterQtdyReal,
                        value: ctrl.footer.qtyReal!.toString(),
                      ),
                    ),
                    if (settingsAppController.settings.hasAverageDiscount ==
                        true)
                      SizedBox(
                        width: DeviceSize.width(90, 145),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterDiscount,
                          value: ctrl.footer.discount!.formatPercent(),
                        ),
                      ),
                    SizedBox(
                      width: DeviceSize.width(120, 165),
                      child: OrderFooterItem(
                        title: AppStrings.orderFooterTotalNet,
                        value: ctrl.footer.totalGross!.formatReal(),
                      ),
                    ),
                    SizedBox(
                      width: DeviceSize.width(120, 165),
                      child: OrderFooterItem(
                        title: AppStrings.orderFooterTotalNet2,
                        value: ctrl.footer.totalNet!.formatReal(),
                      ),
                    ),
                    if (globalParams.getTabloidSelected() != null &&
                        globalParams
                                .getTabloidSelected()
                                ?.orderMaximumQuantity !=
                            null)
                      SizedBox(
                        width: DeviceSize.width(85, 105),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterOrderMax,
                          value: globalParams
                              .getTabloidSelected()!
                              .orderMaximumQuantity!
                              .toString(),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          bottomSheet: const VersionWidget(),
        );
      },
    );
  }
}
