import 'dart:developer';

import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/stores/enuns/visit_status_enum.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class StoreRoutesPlannedController
    extends GetxControllerInstrumentado<StoreRoutesPlannedController>
    with TraceableController {
  StoreRoutesPlannedController();

  List<StoresModel> storesList = [];
  final rx = 0.obs;
  bool isReorderEnabled = false;

  Future<void> toggleReorder() async {
    if (isReorderEnabled) {
      int position = 0;
      for (var item in storesList) {
        log(item.razaoSocial ?? "");
        item.dataExtra!.orderLocal = position;
        item.dataExtra!.order = position;
        position = position + 1;
      }
      await Future.wait(storesList.map((item) async {
        await storeRoutesController.updateStore(item);
      }));
    }
    isReorderEnabled = !isReorderEnabled;
    update();
  }

  void reorderList(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final StoresModel item = storesList.removeAt(oldIndex);
    storesList.insert(newIndex, item);
    update();
  }

  Future<void> resetAndRefetch() async {
    await fetchStores();
    rx.value++;
  }

  void updateData() {
    update();
  }

  Future<void> fetchStores() async {
    final response = await StoresModel().getList(
      hashCode: "planned",
    );

    storesList =
        response.where((x) => x.dataExtra!.canPlanned == true).toList();
    storesList.sort((a, b) =>
        (a.dataExtra!.orderLocal ?? a.dataExtra!.order ?? 0)
            .compareTo(b.dataExtra!.orderLocal ?? a.dataExtra!.order ?? 0));
    rx.value++;
    update();
  }

  Future<void> removeStoreFromRoute(StoresModel item,
      {bool onError = false}) async {
    if (item.dataExtra!.hasRouteSync == true) {
      Dialogs.info(
        "Remover dos Planejados",
        "A visita ao PDV está agendada e não é possível remover da rota.\n\nCaso não tenha conseguido realizar a visita, registre o motivo escolhendo a opção *Não visitado*.\n\nSe foi ao PDV, mas a visita não foi realizada, selecione *Visita Justificada* e explique o motivo.",
        buttonName: "Fechar",
        buttonOnPressed: () async {
          if (onError == true) {
            await item.removeOffline();
            await storeRoutesController.updateStore(item);
            await fetchStores();
          }
          GetC.close();
        },
      );
    } else {
      Dialogs.confirm("Remover dos Planejados",
          "Tem certeza que deseja remover este PDV dos \"Planejados\"?\n\nEle retornará para os resultados de pesquisa na aba \"Meu Painel\".",
          buttonNameCancel: "Cancelar",
          buttonNameOk: "Remover", onPressedCancel: () {
        GetC.close();
      }, onPressedOk: () async {
        await item.removeRoutes();
        await storeRoutesController.updateStore(item);
        await fetchStores();
        GetC.close();
      });
    }
  }

  Future<void> setCanOrderOfflineSync(StoresModel item, bool? v) async {
    item.dataExtra!.canOrderOfflineSync = v;
    await storeRoutesController.updateStore(item);
    update();
  }

  Future<void> setCanVisitaSync(StoresModel item, bool? v) async {
    item.dataExtra!.canVisitaSync = v;
    await storeRoutesController.updateStore(item);
    update();
  }

  //Just debug mode
  Future<void> clearStoresPlanned() async {
    for (var item in storesList) {
      await item.removeRoutes();
      await storeRoutesController.updateStore(item);
    }
    await fetchStores();
  }

  Color getVisitStatusColor(StoresModel data) {
    if (data.dataExtra!.isVisitaSync == true) {
      return Colors.green;
    }

    if (data.dataExtra!.isVisitaCanEdit == true &&
        data.dataExtra!.statusVisit == StatusVisitEnum.partialSaved) {
      return Colors.orange;
    }

    if (data.dataExtra!.isVisitaCanEdit == true &&
        data.dataExtra!.statusVisit == StatusVisitEnum.completeSaved) {
      return Colors.yellow.shade700;
    }
    return Colors.grey.shade700;
  }

  IconData getVisitStatusIcon(StoresModel data) {
    if (data.dataExtra!.isVisitaSync == true) {
      return FontAwesomeIcons.checkDouble;
    }

    if (data.dataExtra!.isVisitaCanEdit == true &&
        data.dataExtra!.statusVisit == StatusVisitEnum.partialSaved) {
      return FontAwesomeIcons.locationDot;
    }

    if (data.dataExtra!.isVisitaCanEdit == true &&
        data.dataExtra!.statusVisit == StatusVisitEnum.completeSaved) {
      return FontAwesomeIcons.locationDot;
    }
    return FontAwesomeIcons.solidSquareCheck;
  }

  String getVisitStatusLabel(StoresModel data) {
    if (data.dataExtra!.isVisitaSync == true) {
      return "Visita Enviada";
    }

    if (data.dataExtra!.isVisitaCanEdit == true &&
        data.dataExtra!.statusVisit == StatusVisitEnum.partialSaved) {
      return "Visita Incompleta";
    }

    if (data.dataExtra!.isVisitaCanEdit == true &&
        data.dataExtra!.statusVisit == StatusVisitEnum.completeSaved) {
      return "Visita Preenchida";
    }
    return "Visita Agendada";
  }

  Future<void> openVisit(StoresModel item) async {
    if (item.dataExtra!.routeId == null) {
      SnackbarCustom.snackbarError("Rota não encontrada");
      return;
    }

    if (item.dataExtra!.roteiroId == null) {
      SnackbarCustom.snackbarWarning("Roteiro não encontrado!");
    }
    globalParams.order.setCurrentStore(item);
    final visitController = Get.find<VisitsController>();

    visitController.loadVisitData(item.dataExtra!.routeId!);
    visitController.currentIndex = 0;

    Get.toNamed(RoutesPath.visits,
        arguments: {'routeId': item.dataExtra!.routeId})?.then((value) {
      storeRoutesController.setHasVisitToSync();
      update();
    });
  }

  Future<void> switchToOnlineMode(StoresModel item) async {
    Dialogs.confirm(
      "Voltar para o Modo Online",
      "Tem certeza que deseja voltar para o modo online?\n\nTodos os dados offline serão perdidos.",
      buttonNameCancel: "Cancelar",
      buttonNameOk: "Confirmar",
      onPressedCancel: () {
        GetC.close();
      },
      onPressedOk: () async {
        await item.removeOffline();
        await storeRoutesController.updateStore(item);
        await fetchStores();
        GetC.close();
      },
    );
  }

  bool hasVisitToSync() {
    return storesList
        .any((x) => x.dataExtra!.statusVisit == StatusVisitEnum.completeSaved);
  }

  String getStoreToVisitToSync() {
    return storesList
        .where((x) => x.dataExtra!.statusVisit == StatusVisitEnum.completeSaved)
        .map((e) => "${e.cNPJ} - ${e.razaoSocial}")
        .toList()
        .join("\n ");
  }
}
