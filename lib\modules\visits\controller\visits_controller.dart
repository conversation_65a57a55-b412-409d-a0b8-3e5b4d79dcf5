import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/converters/converter_base64.dart';
import 'package:pharmalink/core/enuns/storage_enuns.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_custom_action.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/camera_picker/models/camera_picker_model.dart';
import 'package:pharmalink/modules/logs_http/models/logs_http_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_model.dart';
import 'package:pharmalink/modules/researches_merchan_competitive/models/researches_merchan_competitive_model.dart';
import 'package:pharmalink/modules/researches_merchan_industry/models/researches_merchan_industry_model.dart';
import 'package:pharmalink/modules/researches_product_concurrent/models/researches_product_concurrent_model.dart';
import 'package:pharmalink/modules/researches_product_industry/models/researches_product_industry_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_model.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_model.dart';
import 'package:pharmalink/modules/stores/enuns/visit_status_enum.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/visits/models/visit_selected_model.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';
import 'package:pharmalink/modules/visits/models/visits_goals_model.dart';
import 'package:pharmalink/modules/visits/models/visits_info_model.dart';
import 'package:pharmalink/modules/visits/models/visits_search_item.dart';
import 'package:pharmalink/modules/visits/models/visits_status.dart';
import 'package:pharmalink/modules/visits/models/visits_sync_request_model.dart';
import 'package:pharmalink/modules/visits/models/visits_sync_result_model.dart';
import 'package:pharmalink/modules/visits/pages/visits_register_page.dart';
import 'package:pharmalink/modules/visits/pages/visits_search_page.dart';

class VisitsController extends GetxControllerInstrumentado<VisitsController> {
  List<TipoVisita> visitTypes = [];
  VisitsByRoutesResponseModel? currentRouteVisit;
  int? routeId;
  VisitsStatusEnum isCheckinStart = VisitsStatusEnum.pending;
  List<XFile> pictures = [];
  List<VisitsSearchItemModel> visitsSearch = [];
  bool showCheckInPage = false;
  Duration? elapsedTime = Duration.zero;
  bool checkButtonLoad = false;
  Timer? timer;

  late DynatraceCustomAction action;

  int currentIndex = 0;
  final bottomPages = [const VisitsRegisterPage(), const VisitsSearchPage()];

  bool tryAgain = false;

  TextEditingController comentarioController = TextEditingController();
  TextEditingController objetivosController = TextEditingController();

  bool isCameraOpen = false;

  final Map<int, bool> _expandedRoles = {};

  bool isExpanded(int index) => _expandedRoles[index] ?? true;

  void toggleExpanded(int index) {
    _expandedRoles[index] = !(isExpanded(index));
    update();
  }

  void setCameraOpen(bool value) {
    isCameraOpen = value;
    update();
  }

  @override
  void onInit() async {
    action = dynatrace.actionReport(runtimeType, "Visit Page");
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    routeId = Get.arguments?['routeId'];
    if (routeId == null) {
      log('Warning: routeId is null. Make sure it is passed correctly.');
    }
    // // await dbContext.deleteByKey(
    // //     key: DatabaseModels.researchesMerchanCompetitiveAwsers);
    // // await dbContext.deleteByKey(
    // //     key: DatabaseModels.researchesMerchanIndustryAwsers);
    // if (routeId != null) {
    //   loadDomains();
    //   await getShowCheckInPage();
    //   await getData();

    //   initSearch();
    // }
  }

  void resetInfo() {
    showCheckInPage = false;
    pictures = [];
    currentRouteVisit = null;
    comentarioController.text = "";
    objetivosController.text = "";
    tryAgain = false;
  }

  Future<void> loadVisitData(int id) async {
    routeId = id;
    resetInfo();

    loadDomains();
    await getShowCheckInPage();
    await getData();

    initSearch();
  }

  Future<void> setPicture(CameraPickerModel data) async {
    pictures.add(XFile(data.file.path));
    currentRouteVisit!.images =
        pictures
            .map(
              (e) => VisitsSyncRequestVisitImage(
                caminhoCompleto: e.path,
                nomeImagem: e.name,
                orientation: data.orientation,
              ),
            )
            .toList();
    await saveLocal();
    update();
  }

  bool canEdit() {
    if (currentRouteVisit != null) {
      return currentRouteVisit!.idVisita == null;
    }

    return true;
  }

  Future<void> getShowCheckInPage() async {
    var boxInfo = await VisitisInfoModel().getFirstByType(
      workspaceId: appController.workspace!.workspaceId!,
      type: StorageEnums.parametrizacaoCheckIn,
    );

    if (boxInfo == null) {
      showCheckInPage = false;
    } else {
      showCheckInPage = boxInfo.valor == "0" ? false : true;
    }
  }

  Future<void> removePicture(String path) async {
    await Dialogs.confirm(
      AppStrings.attention,
      AppStrings.visitRemoveImage,
      buttonNameCancel: "Não",
      buttonNameOk: "Sim",
      onPressedOk: () async {
        GetC.close();
        pictures.removeWhere((e) => e.path == path);
        currentRouteVisit!.images =
            pictures
                .map(
                  (e) => VisitsSyncRequestVisitImage(
                    caminhoCompleto: e.path,
                    nomeImagem: e.name,
                  ),
                )
                .toList();
        await saveLocal();
        update();
      },
    );
  }

  Future<void> getLocationCheckout() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getLocationCheckout",
    );

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      currentRouteVisit!.longitudeCheckOut = position.longitude;
      currentRouteVisit!.latitudeCheckOut = position.latitude;
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      currentRouteVisit!.longitudeCheckOut = 0;
      currentRouteVisit!.latitudeCheckOut = 0;
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getLocationCheckin() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getLocationCheckin",
    );

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      currentRouteVisit!.longitudeCheckIn = position.longitude;
      currentRouteVisit!.latitudeCheckIn = position.latitude;
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      currentRouteVisit!.longitudeCheckIn = 0;
      currentRouteVisit!.latitudeCheckIn = 0;
      rethrow;
    } finally {
      leaveAction();
    }
  }

  void setCheckButtonLoad(bool v) {
    checkButtonLoad = v;
    update();
  }

  Future<void> setCheckin() async {
    setCheckButtonLoad(true);
    isCheckinStart = VisitsStatusEnum.inProgress;
    currentRouteVisit!.startTime = DateTime.now();
    currentRouteVisit!.endTime = null;

    await getLocationCheckin();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      elapsedTime = DateTime.now().difference(currentRouteVisit!.startTime!);
      update();
    });
    await saveLocal();
    setCheckButtonLoad(false);
  }

  Future<void> setCheckout() async {
    setCheckButtonLoad(true);
    isCheckinStart = VisitsStatusEnum.completed;
    timer?.cancel();
    await getLocationCheckout();
    currentRouteVisit!.endTime = DateTime.now();
    await saveLocal();

    setCheckButtonLoad(false);
  }

  void onBottomSelected(int index) {
    if (currentIndex == 0) {
      saveLocal(hasMessage: true);
    }
    currentIndex = index;
    update();
  }

  void loadDomains() {
    visitTypes = [];
    visitTypes.add(TipoVisita(idTipoVisita: 1, descricao: "Presencial"));
    visitTypes.add(TipoVisita(idTipoVisita: 2, descricao: "Virtual"));
    //update();
  }

  Future<void> setFields(VisitsByRoutesResponseModel data) async {
    currentRouteVisit = data;
    if (data.currentDate == null) {
      currentRouteVisit!.currentDate = DateTime.now();
    }
    currentRouteVisit!.idRota = routeId;
    currentRouteVisit!.idLoja = globalParams.getCurrentStore()!.idLoja;
    currentRouteVisit!.images =
        currentRouteVisit!.images != null ? currentRouteVisit!.images! : [];

    pictures =
        currentRouteVisit!.images != null
            ? currentRouteVisit!.images!
                .map((e) => XFile(e.caminhoCompleto!))
                .toList()
            : [];
    if (currentRouteVisit!.startTime == null) {
      currentRouteVisit!.startTime = DateTime.now();
      isCheckinStart = VisitsStatusEnum.pending;
    }
    if (currentRouteVisit!.endTime != null) {
      elapsedTime = currentRouteVisit!.endTime!.difference(
        currentRouteVisit!.startTime!,
      );
      isCheckinStart = VisitsStatusEnum.completed;
    }
    comentarioController.text = currentRouteVisit!.comentarioVisita ?? "";
    objetivosController.text = currentRouteVisit!.objetivoVisita ?? "";
    _expandedRoles.clear();
    _expandedRoles.addAll({
      for (
        int i = 0;
        i < (currentRouteVisit!.visitSelectedList ?? []).length;
        i++
      )
        i:
            (currentRouteVisit!.visitSelectedList?[i].name.isNotEmpty ?? false)
                ? false
                : true,
    });

    update();
  }

  Future<void> getData() async {
    var (leaveAction, _) = action.subActionReport("Carregando dados");

    final visitsBox = await VisitsByRoutesResponseModel().getFirst(
      routeId: routeId!,
      workspaceId: appController.workspace!.workspaceId!,
    );
    if (visitsBox != null) {
      setFields(visitsBox);

      update();
      return;
    }
    final result = await visitsApi.getVisitsByRoute(routeId: routeId!);
    if (result.error != null) {
      tryAgain = true;
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      await setFields(result.data!);
      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.visitsByRoutesResponseModel,
            data: result.data!,
            storeId: routeId!,
            userId: appController.userLogged!.userId,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true,
          );
    }
    update();

    leaveAction();
  }

  Future<void> getDataSync(int id) async {
    var (leaveAction, _) = action.subActionReport("getDataSync");

    final result = await visitsApi.getVisitsByRoute(routeId: id);
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      final store = storeRoutesController.storeListFull.firstWhereOrNull(
        (e) => e.dataExtra!.routeId != null && e.dataExtra!.routeId == id,
      );
      HttpResponse<List<VisitGoalsModel>> visitGoal = HttpResponse([], null);
      if (store != null) {
        visitGoal = await visitsApi.getVisitGoals(
          routeId: id,
          storeId: store.idPdv!,
        );
      }
      final visitsLocal = await VisitsByRoutesResponseModel().getList(
        workspaceId: appController.workspace!.workspaceId!,
      );
      result.data!.idRota = id;
      result.data!.isSync = SyncEnum.none;

      var visitLocal = visitsLocal.firstWhereOrNull(
        (element) => element.idRota == id,
      );

      if (visitLocal != null) {
        result.data!.isSync = visitLocal.isSync ?? SyncEnum.none;
        result.data!.statusVisit =
            visitLocal.statusVisit ?? StatusVisitEnum.notInitilized;
        result.data!.acompanhamentosVisita = visitLocal.acompanhamentosVisita;
        result.data!.idAcompanhamentoVisitaSelecionado =
            visitLocal.idAcompanhamentoVisitaSelecionado;
        result.data!.acompanhante = visitLocal.acompanhante;
        result.data!.comentarioVisita = visitLocal.comentarioVisita;
        result.data!.dataVisita = visitLocal.dataVisita;
        result.data!.endTime = visitLocal.endTime;
        result.data!.enviar = visitLocal.enviar;
        result.data!.idLoja = visitLocal.idLoja;
        result.data!.idMotivoVisitaSelecionado =
            visitLocal.idMotivoVisitaSelecionado;
        result.data!.idPeriodoVisitaSelecionado =
            visitLocal.idPeriodoVisitaSelecionado;
        result.data!.idStatusVisitaSelecionado =
            visitLocal.idStatusVisitaSelecionado;
        result.data!.images =
            result.data!.visitaImagem == null ? visitLocal.images : null;
        result.data!.latitudeCheckIn = visitLocal.latitudeCheckIn;
        result.data!.latitudeCheckOut = visitLocal.latitudeCheckOut;
        result.data!.longitudeCheckIn = visitLocal.longitudeCheckIn;
        result.data!.longitudeCheckOut = visitLocal.longitudeCheckOut;
        result.data!.motivosVisitaJustificada =
            visitLocal.motivosVisitaJustificada;
        result.data!.motivosVisitaNaoVisitada =
            visitLocal.motivosVisitaNaoVisitada;
        result.data!.objetivoVisita = visitLocal.objetivoVisita;
        result.data!.periodosVisita = visitLocal.periodosVisita;
        result.data!.startTime = visitLocal.startTime;
        result.data!.statusVisita = visitLocal.statusVisita;
        result.data!.tipoVisita = visitLocal.tipoVisita;
        result.data!.currentDate = DateTime.now();
        result.data!.lastVisitGoal =
            (visitGoal.data != null && visitGoal.data!.isEmpty)
                ? visitLocal.lastVisitGoal
                : visitGoal.data?.first.objetivo ?? visitLocal.lastVisitGoal;
      }

      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.visitsByRoutesResponseModel,
            data: result.data!,
            storeId: id,
            userId: appController.userLogged!.userId,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true,
          );
    }

    leaveAction();
  }

  Future<void> refreshVisitsSearch() async {
    await initSearch();
    update();
  }

  Future<void> initSearch() async {
    var (leaveAction, _) = action.subActionReport("initSearch");
    await generalParameterizationController.getData();
    visitsSearch = [];
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaIndustria" && element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Produto Indústria",
          code: "PesquisaIndustria",
          cardColor: await getColorProductIndustry(),
          icon: AppImages.researchIndustry,
          onTap: () async {
            await researchesProductIndustryController.getData(routeId!);
          },
        ),
      );
    }
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaShareOfShelf" && element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Share de Gôndola",
          code: "PesquisaShareOfShelf",
          cardColor: await getColorShare(),
          icon: AppImages.researchShare,
          onTap: () async {
            await researchesShareOfShelfController.getData();
          },
        ),
      );
    }
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaConcorrencia" && element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Produto Concorrente",
          code: "PesquisaConcorrencia",
          cardColor: await getColorProductConcurrent(),
          icon: AppImages.researchConcurrent,
          onTap: () async {
            await researchesProductConcurrentController.getData(routeId!);
          },
        ),
      );
    }
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaMerchanIndustria" &&
          element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Merchan. Indústria",
          code: "PesquisaMerchanIndustria",
          cardColor: await getColorMerchanIndustry(),
          icon: AppImages.researchMerchanIndustry,
          onTap: () async {
            await researchesMerchanIndustryController.getData(routeId!);
          },
        ),
      );
    }
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaMerchanConcorrencia" &&
          element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Merchan. Concorrente",
          code: "PesquisaMerchanConcorrencia",
          cardColor: await getColorMerchanCompetitive(),
          icon: AppImages.researchMerchanConcurrent,
          onTap: () async {
            await researchesMerchanCompetitiveController.getData(routeId!);
          },
        ),
      );
    }
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaComplementar" && element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Complementar",
          code: "PesquisaComplementar",
          cardColor: await getColorComplementary(),
          icon: AppImages.researchComplement,
          onTap: () async {
            await researchesComplementaryController.getData(routeId!);
          },
        ),
      );
    }
    if (generalParameterizationController.accessPermission.any(
      (element) =>
          element.codigo == "PesquisaTradeMarketing" && element.incluir == true,
    )) {
      visitsSearch.add(
        VisitsSearchItemModel(
          title: "Trade Marketing",
          code: "PesquisaTradeMarketing",
          cardColor: await getColorTrade(),
          icon: AppImages.researchTrade,
          onTap: () async {
            await researchesTradeMarketingController.getData(
              storeId: globalParams.getCurrentStore()!.idLoja!,
            );
          },
        ),
      );
    }
    leaveAction();
  }

  Future<Color> getColorMerchanIndustry() async {
    if (routeId == null) return Colors.grey;
    final list = await ResearchesMerchanIndustryDataModel().getList(
      workspaceId: appController.workspace!.workspaceId!,
      routeId: routeId!,
    );
    return list.isEmpty ||
            list.any(
              (element) =>
                  element.isSync == SyncEnum.none ||
                  element.pesquisaMerchandisingItens!.isEmpty,
            )
        ? Colors.grey
        : list.any((element) => element.isSync == SyncEnum.awaited)
        ? Colors.yellow.shade800
        : Colors.green;
  }

  Future<Color> getColorMerchanCompetitive() async {
    if (routeId == null) return Colors.grey;
    final list = await ResearchesMerchanCompetitiveDataModel().getList(
      workspaceId: appController.workspace!.workspaceId!,
      routeId: routeId!,
    );
    return list.isEmpty ||
            list.any(
              (element) =>
                  element.isSync == SyncEnum.none ||
                  element.pesquisaMerchandisingItens!.isEmpty,
            )
        ? Colors.grey
        : list.any((element) => element.isSync == SyncEnum.awaited)
        ? Colors.yellow.shade800
        : Colors.green;
  }

  Future<Color> getColorProductIndustry() async {
    if (routeId == null) return Colors.grey;
    final list = await ResearchesProductIndustryDataModel().getList(
      workspaceId: appController.workspace!.workspaceId!,
      routeId: routeId!,
    );
    return list.isEmpty ||
            list.any((element) => element.isSync == SyncEnum.none)
        ? Colors.grey
        : list.any((element) => element.isSync == SyncEnum.awaited)
        ? Colors.yellow.shade800
        : Colors.green;
  }

  Future<Color> getColorTrade() async {
    final list = await ResearchesTradeMarketingDataModel().getListByStore(
      storeId: globalParams.getCurrentStore()!.idLoja!,
    );
    return list.isEmpty ||
            list.any((element) => element.isSync == SyncEnum.none)
        ? Colors.grey
        : list.any((element) => element.isSync == SyncEnum.awaited)
        ? Colors.yellow.shade800
        : Colors.green;
  }

  Future<Color> getColorShare() async {
    final list = await ResearchesShareOfShelfModel().getList();
    final listStore =
        list
            .where(
              (element) => element.pdvRelacionadosPesquisa!.contains(
                globalParams.getCurrentStore()!.idLoja!,
              ),
            )
            .toList();
    return listStore.isEmpty ||
            listStore.any((element) => element.isSync == SyncEnum.none)
        ? Colors.grey
        : listStore.any((element) => element.isSync == SyncEnum.awaited)
        ? Colors.yellow.shade800
        : Colors.green;
  }

  Future<Color> getColorComplementary() async {
    if (routeId == null) return Colors.grey;
    final list = await ResearchesComplementaryModel().getList(routeId!);
    var totalResearchs = list.length;

    if (list.isEmpty) {
      return Colors.grey;
    }

    if (list.any((element) => element.isSync == SyncEnum.awaited)) {
      return Colors.yellow.shade800;
    }

    if (list.where((element) => element.isSync == SyncEnum.finished).length ==
        totalResearchs) {
      return Colors.green;
    }

    if (list.where((element) => element.isSync == SyncEnum.finished).length !=
            totalResearchs &&
        list.any((element) => element.isSync == SyncEnum.awaited)) {
      return Colors.yellow.shade800;
    }

    return Colors.grey;
  }

  Future<Color> getColorProductConcurrent() async {
    if (routeId == null) return Colors.grey;
    final list = await ResearchesProductConcurrentModel().getList(
      workspaceId: appController.workspace!.workspaceId!,
      routeId: routeId!,
    );
    return list.isEmpty ||
            list.any((element) => element.isSync == SyncEnum.none)
        ? Colors.grey
        : list.any((element) => element.isSync == SyncEnum.awaited)
        ? Colors.yellow.shade800
        : Colors.green;
  }

  void setStatus(int status) {
    var descricaoStatus =
        currentRouteVisit!.statusVisita!
            .firstWhereOrNull((element) => element.idVisitaStatus == status)
            ?.descricao;

    action.reportEvent("Status: $descricaoStatus");

    currentRouteVisit!.idStatusVisitaSelecionado =
        currentRouteVisit!.idStatusVisitaSelecionado == status ? null : status;

    currentRouteVisit!.idMotivoVisitaSelecionado = null;
    currentRouteVisit!.idPeriodoVisitaSelecionado = null;
    currentRouteVisit!.idAcompanhamentoVisitaSelecionado = null;
    currentRouteVisit!.tipoVisita = 0;
    currentRouteVisit!.visitSelectedList = null;
    currentRouteVisit!.objetivoVisita = null;
    objetivosController.clear();
    currentRouteVisit!.comentarioVisita = null;
    comentarioController.clear();
    currentRouteVisit!.acompanhante = null;
    currentRouteVisit!.objetivoVisita = null;
    objetivosController.clear();
    if (status == 2) {
      currentRouteVisit!.images = [];
      saveLocal();
    } else if (status == 3) {
      currentRouteVisit!.visitSelectedList = [VisitSelectedModel.empty()];
      saveLocal();
    }
    update();
  }

  void setPeriodoVisita(int status) {
    var descricaoVisitaPeriodo = currentRouteVisit!.periodosVisita!
        .firstWhereOrNull((element) => element.idVisitaPeriodo == status);
    action.reportEvent("Periodo da Visita: $descricaoVisitaPeriodo");

    currentRouteVisit!.idPeriodoVisitaSelecionado =
        currentRouteVisit!.idPeriodoVisitaSelecionado == status ? null : status;

    currentRouteVisit!.idAcompanhamentoVisitaSelecionado = null;
    currentRouteVisit!.tipoVisita = 0;
    update();
  }

  void setVisitaAcompanhada(int status, int index) {
    final visitaRole = currentRouteVisit!.acompanhamentosVisita!.firstWhere(
      (e) => e.idVisitaAcompanhamento == status,
    );

    action.reportEvent("Visita Acompanhada: $visitaRole");

    final list = currentRouteVisit!.visitSelectedList!;
    list[index] = list[index].copyWith(role: visitaRole);

    _expandedRoles[index] = false;
    update();
  }

  void addVisitSelected() {
    List<VisitSelectedModel> list = currentRouteVisit?.visitSelectedList ?? [];

    list.add(VisitSelectedModel.empty());

    _expandedRoles[list.length - 1] = true;

    update();
  }

  void removeVisitSelected(int index) {
    final list = currentRouteVisit!.visitSelectedList!;

    list.removeAt(index);
    _expandedRoles.remove(index);

    update();
  }

  void setMotivo(int status) {
    var descricaoVisitaAcompanhamento =
        currentRouteVisit!.idStatusVisitaSelecionado == 2
            ? currentRouteVisit!.motivosVisitaNaoVisitada!
                .firstWhereOrNull((element) => element.idVisitaMotivo == status)
                ?.descricao
            : currentRouteVisit!.motivosVisitaJustificada!
                .firstWhereOrNull((element) => element.idVisitaMotivo == status)
                ?.descricao;

    action.reportEvent("Motivo: $descricaoVisitaAcompanhamento");

    currentRouteVisit!.idMotivoVisitaSelecionado =
        currentRouteVisit!.idMotivoVisitaSelecionado == status ? null : status;
    currentRouteVisit!.tipoVisita = 0;
    update();
  }

  void setTipoVisita(int status) {
    var descricaoTipoVisita = visitTypes.firstWhereOrNull(
      (element) => element.idTipoVisita == status,
    );

    action.reportEvent("Tipo da Visita: $descricaoTipoVisita");

    currentRouteVisit!.tipoVisita =
        currentRouteVisit!.tipoVisita == status ? 0 : status;
    update();
  }

  void setAcompanhante(String? v, int index) {
    action.reportEvent("Acompanhante #$index: $v");

    final list = currentRouteVisit!.visitSelectedList!;
    list[index] = list[index].copyWith(name: v ?? "");

    update();
  }

  void setComentarioVisita(String? v) {
    action.reportEvent("Comentario: $v");
    currentRouteVisit!.comentarioVisita = v;
    update();
  }

  void setObjetivoVisita(String? v) {
    action.reportEvent("Objetivo da Proxima Visita: $v");
    currentRouteVisit!.objetivoVisita = v;
    update();
  }

  void setDescricaoImagem(VisitsSyncRequestVisitImage visitImage, String? v) {
    currentRouteVisit!.images
        ?.where((e) => e.nomeImagem == visitImage.nomeImagem)
        .map((e) => e.descricao = v)
        .toList();
    update();
  }

  bool shouldShowPeriodoVisita() {
    return currentRouteVisit!.idStatusVisitaSelecionado == 1 ||
        currentRouteVisit!.idStatusVisitaSelecionado == 3;
  }

  bool shouldShowVisitaAcompanhada() {
    if (currentRouteVisit!.idStatusVisitaSelecionado == 1 ||
        currentRouteVisit!.idStatusVisitaSelecionado == 3) {
      return currentRouteVisit!.idPeriodoVisitaSelecionado != null;
    }

    return false;
  }

  bool shouldShowMotivo() {
    return currentRouteVisit!.idStatusVisitaSelecionado == 3 &&
        currentRouteVisit!.idPeriodoVisitaSelecionado != null &&
        (currentRouteVisit!.visitSelectedList?.length ?? 0) > 0;
  }

  bool shouldShowTipoVisita() {
    return currentRouteVisit!.idPeriodoVisitaSelecionado != null &&
            (currentRouteVisit!.idStatusVisitaSelecionado == 1 &&
                (currentRouteVisit!.visitSelectedList?.length ?? 0) > 0) ||
        (currentRouteVisit!.idStatusVisitaSelecionado == 3 &&
            currentRouteVisit!.idMotivoVisitaSelecionado != null);
  }

  bool shouldShowImage() {
    return currentRouteVisit!.idStatusVisitaSelecionado == 1 ||
        currentRouteVisit!.idStatusVisitaSelecionado == 3;
  }

  bool shouldShowAcompanhante(int index) {
    final selected = currentRouteVisit?.visitSelectedList![index];

    return selected!.role.descricao != 'Não' && selected.role.descricao != null;
  }

  bool shouldShowCometario() {
    return currentRouteVisit!.idStatusVisitaSelecionado != null;
  }

  bool shouldShowObjetivosProximaVisita() {
    return currentRouteVisit!.idStatusVisitaSelecionado != null;
  }

  Future<void> openCameraPicker() async {
    if (!await appController.checkCameraPermission()) {
      Get.toNamed(RoutesPath.permissionRequest)?.then((data) async {
        if (data) {
          await _navigateToCameraPicker();
        }
      });
    } else {
      await _navigateToCameraPicker();
    }
  }

  Future<void> _navigateToCameraPicker() async {
    Get.toNamed(RoutesPath.cameraPicker)?.then((data) async {
      if (data != null) {
        await setPicture(data);
      }
    });
  }

  void openCameraOption() {
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.camera,
                  color: Colors.black,
                ),
                title: const Text('Ir Para Câmera'),
                onTap:
                    isCameraOpen
                        ? null
                        : () async {
                          try {
                            if (!isCameraOpen) {
                              setCameraOpen(true);
                              if (Get.isSnackbarOpen) {
                                await Get.closeCurrentSnackbar();
                              }
                              Future.delayed(
                                const Duration(milliseconds: 150),
                                () async {
                                  Get.back();
                                  await openCameraPicker();
                                  setCameraOpen(false);
                                },
                              );
                            }
                          } catch (e) {
                            setCameraOpen(false);
                          }
                        },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.xmark,
                  color: Colors.black,
                ),
                title: const Text('Cancelar'),
                onTap: () async {
                  GetC.close();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  bool validateVisit({bool isSync = false}) {
    List<String> requiredFields = [];
    if (currentRouteVisit!.idStatusVisitaSelecionado == null) {
      requiredFields.add("Status");
    } else {
      if (currentRouteVisit!.idStatusVisitaSelecionado == 1) {
        if (currentRouteVisit!.idPeriodoVisitaSelecionado == null) {
          requiredFields.add("Período da visita");
        }

        final visitList = currentRouteVisit!.visitSelectedList ?? [];
        if (visitList.isEmpty) {
          requiredFields.add("Pelo menos um acompanhante deve ser adicionado");
        } else {
          for (int i = 0; i < visitList.length; i++) {
            final v = visitList[i];
            if ((v.role.descricao?.toLowerCase() != 'não') &&
                (v.name.trim().isEmpty)) {
              requiredFields.add("Nome do acompanhante #${i + 1}");
            }
          }
        }

        if (currentRouteVisit!.tipoVisita == 0) {
          requiredFields.add("Tipo de visita");
        }
      }

      if (currentRouteVisit!.idStatusVisitaSelecionado == 2) {
        if (currentRouteVisit!.idMotivoVisitaSelecionado == null) {
          requiredFields.add("Motivos");
        }
      }

      if (currentRouteVisit!.idStatusVisitaSelecionado == 3) {
        if (currentRouteVisit!.idPeriodoVisitaSelecionado == null) {
          requiredFields.add("Período da visita");
        }

        if (currentRouteVisit!.idMotivoVisitaSelecionado == null) {
          requiredFields.add("Motivos");
        }

        if (currentRouteVisit!.tipoVisita == 0) {
          requiredFields.add("Tipo de visita");
        }

        final visitList = currentRouteVisit!.visitSelectedList ?? [];
        if (visitList.isEmpty) {
          requiredFields.add("Pelo menos um acompanhante deve ser adicionado");
        } else {
          for (int i = 0; i < visitList.length; i++) {
            final v = visitList[i];
            if ((v.role.descricao?.toLowerCase() != 'não') &&
                (v.name.trim().isEmpty)) {
              requiredFields.add("Nome do acompanhante #${i + 1}");
            }
          }
        }
      }

      if (currentRouteVisit!.comentarioVisita == null ||
          currentRouteVisit!.comentarioVisita!.isEmpty) {
        requiredFields.add("Comentários");
      }
    }

    if (requiredFields.isNotEmpty) {
      if (isSync) {
        SnackbarCustom.snackbarWarning(buildWarningMessage(requiredFields));
      }
      return false;
    } else {
      return true;
    }
  }

  String buildWarningMessage(List<String> requiredFields) {
    var textWarning = 'Campos Obrigatórios não preenchidos: ';
    for (var requiredField in requiredFields) {
      if (!textWarning.contains(requiredField)) {
        textWarning += "${requiredField.toUpperCase()}, ";
      }
    }
    textWarning = "${textWarning.substring(0, textWarning.length - 2)}.";
    return textWarning;
  }

  Future<void> saveLocal({bool? hasMessage}) async {
    var (leaveSubAction, _) = action.subActionReport("saveLocal");
    try {
      currentRouteVisit!.currentDate = DateTime.now();
      currentRouteVisit!.isSync = SyncEnum.awaited;
      globalParams.getCurrentStore()!.dataExtra!.isVisitaCanEdit = true;
      var isCompleteVisit = validateVisit();
      currentRouteVisit!.statusVisit =
          isCompleteVisit
              ? StatusVisitEnum.completeSaved
              : StatusVisitEnum.partialSaved;
      globalParams.getCurrentStore()!.dataExtra!.statusVisit =
          isCompleteVisit
              ? StatusVisitEnum.completeSaved
              : StatusVisitEnum.partialSaved;

      final storeUpdate =
          storeRoutesPlannedController.storesList
              .where(
                (element) =>
                    element.idLoja == globalParams.getCurrentStore()!.idLoja,
              )
              .firstOrNull;
      if (storeUpdate != null) {
        storeUpdate.dataExtra!.statusVisit =
            isCompleteVisit
                ? StatusVisitEnum.completeSaved
                : StatusVisitEnum.partialSaved;

        await storeRoutesController.updateStore(storeUpdate);
      }

      // await storesController.saveListStores();
      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.visitsByRoutesResponseModel,
            data: currentRouteVisit!,
            storeId: routeId!,
            workspaceId: appController.workspace!.workspaceId,
            userId: appController.userLogged!.userId,
            clearCurrentData: true,
          );

      if (hasMessage == true) {
        SnackbarCustom.snackbarSucess("Visita", AppStrings.visitSave);
      }
    } finally {
      leaveSubAction();
    }
    update();
  }

  Future<void> getSyncVisits() async {
    action = dynatrace.actionReport(runtimeType, "Visit Page");

    var (leaveSubAction, subEvent) = action.subActionReport("getSyncVisits");
    try {
      final routesIds =
          storeRoutesPlannedController.storesList
              .where(
                (element) =>
                    element.dataExtra!.routeId != null &&
                    (element.dataExtra!.statusVisit ==
                            StatusVisitEnum.notInitilized ||
                        element.dataExtra!.statusVisit ==
                            StatusVisitEnum.synchronized),
              )
              .map((e) => e.dataExtra!.routeId)
              .toList();

      subEvent.reportValue(
        "Quantidade routesIds a sincronizar",
        routesIds.length,
      );

      currentRouteVisit?.visitSelectedList ??= [VisitSelectedModel.empty()];

      for (var e in routesIds) {
        await getDataSync(e!);
      }
    } finally {
      leaveSubAction();
    }
  }

  Future<void> syncVisitsInfo() async {
    var (leaveSubAction, _) = action.subActionReport("syncVisitsInfo");
    try {
      final result = await visitsApi.getVisitsInfo();

      if (result.error != null) {
        SnackbarCustom.snackbarError(result.error!.message!);
      } else {
        await dbContext
            .withControllerAction(this)
            .addData(
              key: DatabaseModels.visitisInfoModel,
              data: result.data,
              userId: appController.userLogged!.userId,
              workspaceId: appController.workspace!.workspaceId,
              clearCurrentData: true,
            );
      }
    } finally {
      leaveSubAction();
    }
  }

  Future<StoresModel?> getStoreToSync(int storeId) async {
    var (leaveSubAction, _) = action.subActionReport("getStoreToSync");
    var store =
        storeRoutesPlannedController.storesList
            .where((element) => element.idLoja == storeId)
            .firstOrNull;

    if (store != null &&
        (store.dataExtra!.roteiroId == null ||
            store.dataExtra!.roteiroId == 0)) {
      store =
          storeRoutesController.storeListFull
              .where((element) => element.idLoja == storeId)
              .firstOrNull;
    }

    leaveSubAction();
    return store;
  }

  Future<bool> hasVisitToSync() async {
    final visitsAll = await VisitsByRoutesResponseModel().getListToSync(
      workspaceId: appController.workspace!.workspaceId!,
    );

    final listVisits =
        visitsAll
            .where(
              (element) =>
                  element.isSync == SyncEnum.awaited &&
                  (element.statusVisit == StatusVisitEnum.notInitilized ||
                      element.statusVisit == StatusVisitEnum.synchronized),
            )
            .toList();
    return listVisits.isNotEmpty;
  }

  Future<bool> syncVisits() async {
    action = dynatrace.actionReport(runtimeType, "Visit Page");
    StoresModel? storeToUpdate;

    var (leaveSubAction, subEvent) = action.subActionReport("syncVisits");

    try {
      List<VisitsSyncRequestModel> list = [];
      final visitsAll = await VisitsByRoutesResponseModel().getListToSync(
        workspaceId: appController.workspace!.workspaceId!,
      );

      final listVisits =
          visitsAll
              .where(
                (element) =>
                    element.isSync == SyncEnum.awaited &&
                    element.statusVisit == StatusVisitEnum.completeSaved,
              )
              .toList();
      List<VisitsByRoutesResponseModel> listVisitsIncomplete = [];
      subEvent.reportValue(
        "Quantidade de vistas a sincronizar",
        listVisitsIncomplete.length,
      );

      listVisitsIncomplete =
          visitsAll
              .where(
                (element) =>
                    element.isSync == SyncEnum.awaited &&
                    element.statusVisit == StatusVisitEnum.partialSaved,
              )
              .toList();

      subEvent.reportValue(
        "Quantidade de vistas Incompletas a sincronizar",
        listVisitsIncomplete.length,
      );

      if (listVisitsIncomplete.isNotEmpty) {
        SnackbarCustom.snackbarWarning(
          "Algumas visitas não foram enviadas por terem campos obrigatórios não preenchidos!",
        );
      }

      if (listVisits.isEmpty) {
        return true;
      }

      for (var itemSync in listVisits) {
        List<VisitsSyncRequestVisitImage>? images = [];
        if (itemSync.images != null && itemSync.images!.isNotEmpty) {
          images = await Future.wait(
            itemSync.images!.map((e) async {
              return VisitsSyncRequestVisitImage(
                descricao: e.descricao,
                arquivo: await ConverterBase64.getImageBase64(
                  e.caminhoCompleto!,
                ),
                nomeImagem: e.nomeImagem,
                caminhoCompleto: e.caminhoCompleto!,
              );
            }),
          );
        }

        final currentStore = await getStoreToSync(itemSync.idLoja!);

        final model = VisitsSyncRequestModel(
          idRota: itemSync.idRota,
          dataInclusao: DateTime.now(),
          idRoteiro: currentStore?.dataExtra!.roteiroId,
          idVisita: itemSync.idVisita,
          mensagem: itemSync.mensagem,
          ordem: currentStore?.dataExtra!.order,
          comentario: itemSync.comentarioVisita ?? "",
          acompanhante: itemSync.acompanhante,
          objetivo: itemSync.objetivoVisita ?? "",
          dataCheckIn: itemSync.startTime,
          dataCheckOut: itemSync.endTime,
          enviado: true,
          idPdv: itemSync.idLoja,
          idVisitaAcompanhamento: itemSync.idAcompanhamentoVisitaSelecionado,
          idVisitaMotivo: itemSync.idMotivoVisitaSelecionado,
          idVisitaPeriodo: itemSync.idPeriodoVisitaSelecionado,
          //idVisitaMobile: itemSync.idVisitaMobile,
          idVisitaStatus: itemSync.idStatusVisitaSelecionado,
          latitudeCheckIn: itemSync.latitudeCheckIn ?? 0,
          latitudeCheckOut: itemSync.latitudeCheckOut ?? 0,
          longitudeCheckIn: itemSync.longitudeCheckIn ?? 0,
          longitudeCheckOut: itemSync.longitudeCheckOut ?? 0,
          tipoVisita: itemSync.tipoVisita,
          visita: true,
          visitaImagem: images,
          visitSelectedList: itemSync.visitSelectedList,
        );
        if (model.idRoteiro != null) {
          list.add(model);
        } else {
          //limpar as imagens para não salvar no log as imagens e causar erro do SQLITE
          if (model.visitaImagem != null) {
            model.visitaImagem!.map((e) {
              e.arquivo = "";
            }).toList();
          }
          logsHttpController.addLog(
            LogsHttpModel(
              workspaceId: appController.workspace?.workspaceId,
              workspaceName: appController.workspace?.name,
              userName: appController.userLogged?.userName ?? "",
              url: "VisitsController/SyncVisits",
              headers: "VisitsSyncRequestModel - com roteiro vazio",
              request: jsonEncode(model),
              response: null,
              createdAt: DateTime.now(),
              method: "Post",
              statusCode: 500,
              isFlutterError: false,
            ),
          );
        }
      }

      if (list.isEmpty) {
        SnackbarCustom.snackbarError(
          "Ocorreu um problema durante sincronização de visita. tente novamente!",
        );

        return false;
      }

      List<VisitSyncResultModel> visitSincResults = [];
      List<String?> storeNamesWithUnsuccesfullSync = [];

      final result = await visitsApi.syncVisits(model: list);

      if (result.error != null) {
        SnackbarCustom.snackbarError(result.error!.message ?? "");
        return false;
      } else {
        visitSincResults = result.data!;
        for (var lv in listVisits) {
          var dataSave =
              visitsAll
                  .where(
                    (va) => va.idLoja == lv.idLoja && va.idRota == lv.idRota,
                  )
                  .first;
          var visitSyncResult = visitSincResults.firstWhereOrNull(
            (element) => element.idRota == lv.idRota,
          );

          storeToUpdate =
              storeRoutesPlannedController.storesList
                  .where((element) => element.idLoja == lv.idLoja)
                  .first;

          if (visitSyncResult != null) {
            if (!visitSyncResult.success!) {
              storeNamesWithUnsuccesfullSync.add(storeToUpdate.razaoSocial);
            }

            dataSave.isSync =
                visitSyncResult.success! ? SyncEnum.finished : SyncEnum.awaited;
            dataSave.statusVisit =
                visitSyncResult.success!
                    ? StatusVisitEnum.synchronized
                    : StatusVisitEnum.completeSaved;
          } else {
            dataSave.isSync = SyncEnum.awaited;
            dataSave.statusVisit = StatusVisitEnum.completeSaved;
          }

          storeToUpdate.dataExtra!.isVisitaSync = true;
          storeToUpdate.dataExtra!.statusVisit =
              visitSyncResult != null && visitSyncResult.success!
                  ? StatusVisitEnum.synchronized
                  : StatusVisitEnum.completeSaved;

          await storeRoutesController.updateStore(storeToUpdate);

          await dbContext
              .withControllerAction(this)
              .addData(
                key: DatabaseModels.visitsByRoutesResponseModel,
                data: dataSave,
                storeId: dataSave.idRota,
                userId: appController.userLogged!.userId,
                workspaceId: appController.workspace!.workspaceId,
                clearCurrentData: true,
              );

          if (storeNamesWithUnsuccesfullSync.isNotEmpty) {
            SnackbarCustom.snackbarWarningWithFields(
              "As seguinte lojas tiveram problema na sincronização:",
              fieldsToConcat: storeNamesWithUnsuccesfullSync,
            );
          }
        }
        update();
        return true;
      }
    } finally {
      leaveSubAction();
    }
  }

  bool isEnabled() {
    if (currentRouteVisit != null) {
      return currentRouteVisit!.isSync != SyncEnum.finished;
    }
    return true;
  }
}
