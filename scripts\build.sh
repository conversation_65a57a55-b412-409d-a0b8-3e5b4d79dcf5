#!/usr/bin/env bash

# <PERSON><PERSON> padrão (opcional)
TYPE=""
FLAVOR=""
ENVIRONMENT=""
MODE=""
MODE_XCODE=""
OUTPUT_DIR=""
VERSION=""
BUILD_ALL=0

function colored_print() {
  COR=$1
  MSG=$2
  
  case $COR in
    "red")
      echo -e "\033[0;31m${MSG}\033[0m"
      ;;
    "green")
      echo -e "\033[0;32m${MSG}\033[0m"
      ;;
    "yellow")
      echo -e "\033[0;33m${MSG}\033[0m"
      ;;
    "blue")
      echo -e "\033[0;34m${MSG}\033[0m"
      ;;
    *)
      echo "${MSG:-$COR}"
      ;;
  esac

}

# Função para exibir ajuda
usage() {
  colored_print red "Uso: $(basename "$0") -t|--type [apk|aab|appbundle|ipa|ios] -f|--flavor [pharmalink|itrade] -e|--env|--environment [developer|staging|production]"
  colored_print red "Uso: $(basename "$0") -a|--all"
  exit 1
}

# Parse de argumentos
colored_print  "Analisando argumentos..."
colored_print  "Argumentos recebidos: $*"
colored_print  "Argumentos recebidos: $#"
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -a|--all)
      BUILD_ALL=1
      shift 1
      ;;
    -t|--type)
      case $2 in
        aab|appbundle)
          TYPE="appbundle"
          ;;
        ipa|ios)
          TYPE="ipa"
          ;;
        *)
          TYPE="$2"
          ;;
      esac
      shift 2
      ;;
    -f|--flavor)
      FLAVOR="$2"
      shift 2
      ;;
    -e|--env|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    -*)
      echo "❌ Opção desconhecida: $1"
      usage
      ;;
    *)
      echo "❌ Argumento inválido: $1"
      usage
      ;;
  esac
done

# Se BUILD_ALL for 1, executa todas as builds possiveis
if [[ $BUILD_ALL -eq 1 ]]; then
  colored_print green "Executando todas as builds possíveis..."

  for _TYPE in apk aab ipa; do
    for _FLAVOR in pharmalink itrade; do
      for _ENVIRONMENT in developer staging production; do
        colored_print green "Iniciando build para: $_TYPE, $_FLAVOR, $_ENVIRONMENT"
        
        # Chama o script com os parâmetros necessários
        "$0" --type "$_TYPE" --flavor "$_FLAVOR" --environment "$_ENVIRONMENT"
        if [[ $? -ne 0 ]]; then
          colored_print red "❌ Build falhou para: $_TYPE, $_FLAVOR, $_ENVIRONMENT"
          exit 1
        fi
        
        colored_print green "✅ Build concluída com sucesso para: $_TYPE, $_FLAVOR, $_ENVIRONMENT"
      done
    done
  done
  
  colored_print green "✅ Todas as builds foram executadas com sucesso!"
  
  exit 0
fi

# Validação obrigatória
if [[ -z "$TYPE" || -z "$FLAVOR" || -z "$ENVIRONMENT" ]]; then
  colored_print red "❌ Todos os parâmetros são obrigatórios."
  usage
fi

# Validação de valores
if [[ "$TYPE" != "apk" && "$TYPE" != "aab" && "$TYPE" != "appbundle" && "$TYPE" != "ipa" && "$TYPE" != "ios" ]]; then
  colored_print red  "❌ Tipo inválido: $TYPE. Use 'apk', 'aab', 'appbundle', 'ipa' ou 'ios'."
  usage
fi

if [[ "$FLAVOR" != "pharmalink" && "$FLAVOR" != "itrade" ]]; then
  colored_print red  "❌ Flavor inválido: $FLAVOR. Use 'pharmalink' ou 'itrade'."
  usage
fi

if [[ "$ENVIRONMENT" != "developer" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
  colored_print red  "❌ Ambiente inválido: $ENVIRONMENT. Use 'developer', 'staging' ou 'production'."
  usage
fi

case $ENVIRONMENT in
  production)
    MODE_XCODE="Release"
    MODE="release"
  ;;
  staging)
    MODE_XCODE="Profile"
    MODE="profile"
  ;;
  *)
    MODE_XCODE="Debug"
    MODE="debug"
esac

BASE_PRJ_PATH=$(pwd)
while [ "$BASE_PRJ_PATH" != "/" ]; do
  if [ -f "$BASE_PRJ_PATH/pubspec.yaml" ]; then
    colored_print green "Diretorio do projeto encontrado: $BASE_PRJ_PATH"
    cd "$BASE_PRJ_PATH" || colored_print red "Erro ao mudar para o diretório $BASE_PRJ_PATH"
    break
  else
    BASE_PRJ_PATH=$(dirname "$BASE_PRJ_PATH")
  fi
done

OUTPUT_DIR="$BASE_PRJ_PATH/dist"
VERSION=$(grep '^version:' pubspec.yaml | cut -d ' ' -f2)


# Exibe os valores recebidos
colored_print green  "✅ Tipo: $TYPE"
colored_print green  "✅ Flavor: $FLAVOR"
colored_print green  "✅ Ambiente: $ENVIRONMENT"
colored_print green  "✅ Modo: $MODE"
colored_print green  "✅ Modo Xcode: $MODE_XCODE"
colored_print green  "✅ Diretorio do projeto: $BASE_PRJ_PATH"
colored_print green  "✅ Versão: $VERSION"
colored_print green  "✅ Diretório de saída: $OUTPUT_DIR"


function clean {
  colored_print green  "Executando flutter clean..."
  flutter clean
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red  "Erro ao executar flutter clean"
      exit $status
  fi
}

function pub_get {
  colored_print green  "Executando flutter pub get..."
  flutter pub get
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red  "Erro ao executar flutter pub get"
      exit $status
  fi
}

function run_dependencies_configurations() {
  colored_print yellow  "Executando configurações de dependências..."
  
  colored_print yellow  "Executando flutter pub run flutter_flavorizr -f..."
  flutter pub run flutter_flavorizr -f
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar comando flutter pub run flutter_flavorizr -f"
      exit $status
  fi
  
  colored_print yellow  "Executando dart run flutter_launcher_icons -f..."
  dart run flutter_launcher_icons -f "${BASE_PRJ_PATH}/flutter_launcher_icons-${FLAVOR}.yaml"
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar comando dart run flutter_launcher_icons -f..."
      exit $status
  fi

  
  
  colored_print yellow  "Executando dart run flutter_native_splash:create --flavor..."
  dart run flutter_native_splash:create --flavor "${FLAVOR}"
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar comando dart run flutter_native_splash:create --flavor..."
      exit $status
  fi
}

function build_comum {
  colored_print green  "Executando build $TYPE --flavor $FLAVOR --dart-define=ENV=$ENVIRONMENT..."
  flutter build "$TYPE" \
    --flavor "$FLAVOR" \
    --dart-define=ENV="$ENVIRONMENT" \
    --$MODE
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red  "Erro ao executar flutter build"
      exit $status
  fi

  mkdir -p "$OUTPUT_DIR"
  find ./build/app/outputs/flutter-apk/ -type f \( -name "*.apk" \) -exec mv -f -v {} "${OUTPUT_DIR}/app-android-${FLAVOR}-v${VERSION}-${MODE}.apk" \;
  find ./build/app/outputs/bundle/ -type f \( -name "*.aab" \) -exec mv -f -v {} "${OUTPUT_DIR}/app-android-${FLAVOR}-v${VERSION}-${MODE}.aab" \;

  colored_print green  "Build concluído com sucesso!"
}

function install_ruby_env {
  export BASE_RBENV_PATH_INSTALL="/tmp"
  export RBENV_ROOT="$BASE_RBENV_PATH_INSTALL/.rbenv"

#  rm -Rf "$RBENV_ROOT"
  if [ -d "$RBENV_ROOT" ]; then
    colored_print yellow  "Diretorio $RBENV_ROOT ja existe. Pulando instalacao do ruby"
  else
    echo $RBENV_ROOT
    git clone https://github.com/rbenv/rbenv.git "$RBENV_ROOT"
    status=$?
    if [ $status -ne 0 ]
    then
        colored_print red "Erro ao executar git clone do rbenv"
        exit $status
    fi
  fi

  export PATH="$RBENV_ROOT/bin:$PATH"
  eval "$(rbenv init - bash)"
  colored_print yellow  "Versao rbenv: $(rbenv --version)"

  export PATH_RUBY_BUILD="$RBENV_ROOT/plugins/ruby-build"
  if [ -d "$PATH_RUBY_BUILD" ]; then
    colored_print yellow  "Diretorio $PATH_RUBY_BUILD ja existe. Pulando instalacao do ruby-build"
  else
    git clone https://github.com/rbenv/ruby-build.git "$PATH_RUBY_BUILD"
    status=$?
    if [ $status -ne 0 ]
    then
        colored_print red "Erro ao executar git clone do ruby-build"
        exit $status
    fi

    rbenv install 3.1.1
    status=$?
    if [ $status -ne 0 ]
    then
        colored_print red "Erro ao executar rbenv install"
        exit $status
    fi
  fi

  rbenv local 3.1.1
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar rbenv local"
      exit $status
  fi
  colored_print yellow "Versao ruby: $(ruby --version)"

  export GEM_HOME="$BASE_RBENV_PATH_INSTALL/.gem"
  export PATH="$GEM_HOME/bin:$PATH"

  gem install cocoapods
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar gem install cocoapods"
      exit $status
  fi
  
  colored_print yellow "Versao pod: $(pod --version)"
  # pod update AppAuth Dynatrace --repo-update
  pod install --repo-update
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar pod install --repo-update"
      exit $status
  fi

  # pod update AppAuth
  # status=$?
  # if [ $status -ne 0 ]
  # then
  #     colored_print red "Erro ao executar pod update AppAuth"
  #     exit $status
  # fi
}

function build_ios {
  # Caminhos
  PROJECT_PATH="$BASE_PRJ_PATH/ios"
  WORKSPACE_PATH="$PROJECT_PATH/Runner.xcworkspace"
  SCHEME=$FLAVOR
  CONFIGURATION="$MODE_XCODE"
  ARCHIVE_PATH="$PROJECT_PATH/build/Runner.xcarchive"
  EXPORT_OPTIONS_PLIST="$PROJECT_PATH/ExportOptions.plist"
  EXPORT_PATH="${OUTPUT_DIR}/app-ios-${FLAVOR}-v${VERSION}-${MODE_XCODE}/"
  export DEVELOPMENT_TEAM=MZPUV9NQYE

  colored_print yellow "Executando pod install..."
  cd ios || colored_print red "Erro ao mudar para o diretório $BASE_PRJ_PATH/ios"
  install_ruby_env
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar pod install"
      exit $status
  fi
  cd .. || colored_print red "Erro ao mudar para o diretório $BASE_PRJ_PATH"

  colored_print green "Executando flutter build ios..."
  flutter build "$TYPE" \
    --dart-define=ENV="$ENVIRONMENT" \
    --dart-define=DEVELOPMENT_TEAM=$DEVELOPMENT_TEAM \
    --flavor "$FLAVOR" \
    --no-codesign \
    --$MODE
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar flutter build"
      exit $status
  fi

  # colored_print green "Limpando o diretório de construção..."
  # xcodebuild clean \
  #   -workspace "$WORKSPACE_PATH" \
  #   -scheme "${SCHEME}" \
  #   -configuration "$CONFIGURATION"
  # status=$?
  # if [ $status -ne 0 ]
  # then
  #     colored_print red "Erro ao executar xcodebuild clean"
  #     exit $status
  # fi

  colored_print green "Arquivando o projeto..."
  xcodebuild archive \
    -workspace "$WORKSPACE_PATH" \
    -scheme "${SCHEME}" \
    -configuration "$CONFIGURATION" \
    -archivePath "$ARCHIVE_PATH" \
    -destination 'generic/platform=iOS'
  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar xcodebuild archive"
      exit $status
  fi

  # Verificar se o arquivo de arquivamento foi gerado
  if [ ! -d "$ARCHIVE_PATH" ]; then
      colored_print red "Erro: Arquivamento falhou, o diretório $ARCHIVE_PATH não foi encontrado."
      exit 1
  fi

  colored_print greeen "Exportando o IPA..."
  xcodebuild \
    -exportArchive \
    -archivePath "$ARCHIVE_PATH" \
    -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" \
    -exportPath "$EXPORT_PATH"

  status=$?
  if [ $status -ne 0 ]
  then
      colored_print red "Erro ao executar xcodebuild -exportArchive"
      exit $status
  fi

  # Verificar se algum arquivo IPA foi gerado
  IPA_FILE=$(find "$EXPORT_PATH" -name "*.ipa" | head -n 1)
  if [ -z "$IPA_FILE" ]; then
      colored_print red "Erro: Exportação falhou, nenhum arquivo IPA foi encontrado em $EXPORT_PATH."
      exit 1
  fi


  colored_print green "Build and export complete. IPA is located at: $IPA_FILE"

  # Apagar o diretório ios/build
  colored_print green "Apagando o diretório ios/build..."
  rm -rf "$PROJECT_PATH/build"

  colored_print green "Pasta ios/build apagada com sucesso."
}


clean

pub_get

# run_dependencies_configurations

if [[ "$TYPE" == "ipa" ]]; then
  build_ios
else
  build_comum
fi
