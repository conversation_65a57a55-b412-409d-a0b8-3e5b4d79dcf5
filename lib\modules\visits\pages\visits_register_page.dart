import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/camera_picker/widgets/rotated_image_preview.dart';
import 'package:pharmalink/modules/visits/models/visits_status.dart';
import 'package:pharmalink/modules/visits/pages/components/visit_info_column.dart';
import 'package:pharmalink/widgets/buttons/button_with_icon.dart';

class VisitsRegisterPage extends StatelessWidget {
  const VisitsRegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VisitsController>(
      builder: (ctx) {
        if (ctx.currentRouteVisit?.visitSelectedList == null ||
            ctx.currentRouteVisit!.visitSelectedList!.isEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ctx.addVisitSelected();
          });
        }
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Visibility(
                  visible: ctx.isEnabled() && ctx.canEdit(),
                  child: CustomInkWell(
                    onTap: () async {
                      await ctx.saveLocal(hasMessage: true);
                    },
                    child: const Align(
                      alignment: Alignment.centerRight,
                      child: Column(
                        children: [
                          Icon(FontAwesomeIcons.floppyDisk, size: 20),
                          LabelWidget(title: "Salvar", fontSize: 12),
                        ],
                      ),
                    ),
                  ),
                ),
                10.toHeightSpace(),
                const LabelWidget(
                  title: "Objetivos da visita",
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                10.toHeightSpace(),
                Container(
                  color: Colors.grey.shade300,
                  padding: EdgeInsets.all(8.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ctx.currentRouteVisit!.lastVisitGoal != null &&
                              ctx.currentRouteVisit!.lastVisitGoal!.isNotEmpty
                          ? LabelWidget(
                            title: ctx.currentRouteVisit!.lastVisitGoal!,
                            fontSize: 14,
                          )
                          : const LabelWidget(
                            title: "Sem descrição",
                            fontSize: 14,
                          ),
                      Align(
                        alignment: Alignment.centerRight,
                        child: LabelWidget(
                          title:
                              ctx.currentRouteVisit!.startTime?.formatDate(
                                formatType: DateFormatType.ddMMyyyy,
                                applyTimezone: true,
                              ) ??
                              "-",
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(thickness: 2),
                5.toHeightSpace(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    LabelValueVerticalWidget(
                      title: "Início",
                      value:
                          ctx.currentRouteVisit!.startTime?.formatDate(
                            formatType: DateFormatType.hHmmss,
                            applyTimezone: true,
                          ) ??
                          "-",
                    ),
                    LabelValueVerticalWidget(
                      title: "Tempo total",
                      value: ctx.elapsedTime?.formatDuration() ?? "-",
                    ),
                    LabelValueVerticalWidget(
                      title: "Fim",
                      value:
                          ctx.currentRouteVisit!.endTime?.formatDate(
                            formatType: DateFormatType.hHmmss,
                            applyTimezone: true,
                          ) ??
                          "-",
                    ),
                  ],
                ),
                5.toHeightSpace(),
                Visibility(
                  visible:
                      ctx.canEdit() &&
                      ctx.isCheckinStart != VisitsStatusEnum.completed,
                  child: const Divider(thickness: 2),
                ),
                Visibility(
                  visible:
                      ctx.showCheckInPage &&
                      ctx.canEdit() &&
                      ctx.isCheckinStart != VisitsStatusEnum.completed,
                  child: PrimaryButtonWidget(
                    titleButtom:
                        ctx.isCheckinStart == VisitsStatusEnum.inProgress
                            ? 'Fazer Check-out'.toUpperCase()
                            : 'Fazer Check-in'.toUpperCase(),
                    buttonColor:
                        ctx.isCheckinStart == VisitsStatusEnum.inProgress
                            ? Colors.yellow.shade700
                            : Colors.green.shade500,
                    borderRadius: 0,
                    isLoading: ctx.checkButtonLoad,
                    onTap:
                        ctx.isCheckinStart == VisitsStatusEnum.inProgress
                            ? ctx.setCheckout
                            : ctx.setCheckin,
                  ),
                ),
                5.toHeightSpace(),
                const Divider(thickness: 2),
                5.toHeightSpace(),
                //carregar as questões para serem respondidas
                _buildQuestions(ctx),
              ],
            ),
          ),
        );
      },
      dispose: (state) {
        state.controller?.action.leaveRemainingActions();
      },
    );
  }

  Widget _buildQuestions(VisitsController ctrl) {
    if (ctrl.currentRouteVisit == null) {
      return const Padding(
        padding: EdgeInsets.only(top: 50.0),
        child: Center(child: CircularProgressIndicator.adaptive()),
      );
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            LabelWidget(
              title: "Status ",
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            LabelWidget(
              title: "*",
              fontSize: 14,
              fontWeight: FontWeight.bold,
              textColor: Colors.red,
            ),
          ],
        ),
        ...ctrl.currentRouteVisit!.statusVisita!
            .where(
              (element) =>
                  element.idVisitaStatus ==
                      ctrl.currentRouteVisit!.idStatusVisitaSelecionado ||
                  ctrl.currentRouteVisit!.idStatusVisitaSelecionado == null,
            )
            .map(
              (e) => CustomInkWell(
                onTap:
                    ctrl.canEdit() && ctrl.isEnabled()
                        ? () {
                          ctrl.setStatus(e.idVisitaStatus!);
                        }
                        : null,
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Container(
                    width: MediaQuery.sizeOf(Get.context!).width,
                    color:
                        ctrl.currentRouteVisit!.idStatusVisitaSelecionado ==
                                e.idVisitaStatus
                            ? themesController.getPrimaryColor()
                            : Colors.white,
                    padding: const EdgeInsets.all(8.0),
                    child: LabelWidget(
                      title: e.descricao ?? "-",
                      fontSize: 15,
                      textColor:
                          ctrl.currentRouteVisit!.idStatusVisitaSelecionado ==
                                  e.idVisitaStatus
                              ? Colors.white
                              : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
        //Carregar Periodo da Visista
        Visibility(
          visible: ctrl.shouldShowPeriodoVisita(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              5.toHeightSpace(),
              const Divider(thickness: 2),
              5.toHeightSpace(),
              const Row(
                children: [
                  LabelWidget(
                    title: "Período da visita ",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  LabelWidget(
                    title: "*",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.red,
                  ),
                ],
              ),
              ...ctrl.currentRouteVisit!.periodosVisita!
                  .where(
                    (element) =>
                        element.idVisitaPeriodo ==
                            ctrl
                                .currentRouteVisit!
                                .idPeriodoVisitaSelecionado ||
                        ctrl.currentRouteVisit!.idPeriodoVisitaSelecionado ==
                            null,
                  )
                  .map(
                    (e) => CustomInkWell(
                      onTap:
                          ctrl.canEdit() && ctrl.isEnabled()
                              ? () {
                                ctrl.setPeriodoVisita(e.idVisitaPeriodo!);
                              }
                              : null,
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Container(
                          width: MediaQuery.sizeOf(Get.context!).width,
                          color:
                              ctrl
                                          .currentRouteVisit!
                                          .idPeriodoVisitaSelecionado ==
                                      e.idVisitaPeriodo
                                  ? themesController.getPrimaryColor()
                                  : Colors.white,
                          padding: const EdgeInsets.all(8.0),
                          child: LabelWidget(
                            title: e.descricao ?? "-",
                            fontSize: 15,
                            textColor:
                                ctrl
                                            .currentRouteVisit!
                                            .idPeriodoVisitaSelecionado ==
                                        e.idVisitaPeriodo
                                    ? Colors.white
                                    : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
            ],
          ),
        ),

        //Carregar Motivos
        Visibility(
          visible: ctrl.currentRouteVisit!.idStatusVisitaSelecionado == 2,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              5.toHeightSpace(),
              const Divider(thickness: 2),
              5.toHeightSpace(),
              const Row(
                children: [
                  LabelWidget(
                    title: "Motivos ",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  LabelWidget(
                    title: "*",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.red,
                  ),
                ],
              ),
              ...ctrl.currentRouteVisit!.motivosVisitaNaoVisitada!
                  .where(
                    (element) =>
                        element.idVisitaMotivo ==
                            ctrl.currentRouteVisit!.idMotivoVisitaSelecionado ||
                        ctrl.currentRouteVisit!.idMotivoVisitaSelecionado ==
                            null,
                  )
                  .map(
                    (e) => CustomInkWell(
                      onTap:
                          ctrl.canEdit() && ctrl.isEnabled()
                              ? () {
                                ctrl.setMotivo(e.idVisitaMotivo!);
                              }
                              : null,
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Container(
                          width: MediaQuery.sizeOf(Get.context!).width,
                          color:
                              ctrl
                                          .currentRouteVisit!
                                          .idMotivoVisitaSelecionado ==
                                      e.idVisitaMotivo
                                  ? themesController.getPrimaryColor()
                                  : Colors.white,
                          padding: const EdgeInsets.all(8.0),
                          child: LabelWidget(
                            title: e.descricao ?? "-",
                            fontSize: 15,
                            textColor:
                                ctrl
                                            .currentRouteVisit!
                                            .idMotivoVisitaSelecionado ==
                                        e.idVisitaMotivo
                                    ? Colors.white
                                    : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
            ],
          ),
        ),

        //Carregar Motivos
        Visibility(
          visible: ctrl.shouldShowMotivo(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              5.toHeightSpace(),
              const Divider(thickness: 2),
              5.toHeightSpace(),
              const Row(
                children: [
                  LabelWidget(
                    title: "Motivos ",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  LabelWidget(
                    title: "*",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.red,
                  ),
                ],
              ),
              ...ctrl.currentRouteVisit!.motivosVisitaJustificada!
                  .where(
                    (element) =>
                        element.idVisitaMotivo ==
                            ctrl.currentRouteVisit!.idMotivoVisitaSelecionado ||
                        ctrl.currentRouteVisit!.idMotivoVisitaSelecionado ==
                            null,
                  )
                  .map(
                    (e) => CustomInkWell(
                      onTap:
                          ctrl.canEdit() && ctrl.isEnabled()
                              ? () {
                                ctrl.setMotivo(e.idVisitaMotivo!);
                              }
                              : null,
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Container(
                          width: MediaQuery.sizeOf(Get.context!).width,
                          color:
                              ctrl
                                          .currentRouteVisit!
                                          .idMotivoVisitaSelecionado ==
                                      e.idVisitaMotivo
                                  ? themesController.getPrimaryColor()
                                  : Colors.white,
                          padding: const EdgeInsets.all(8.0),
                          child: LabelWidget(
                            title: e.descricao ?? "-",
                            fontSize: 15,
                            textColor:
                                ctrl
                                            .currentRouteVisit!
                                            .idMotivoVisitaSelecionado ==
                                        e.idVisitaMotivo
                                    ? Colors.white
                                    : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
            ],
          ),
        ),
        //Tipo de visita
        Visibility(
          visible: ctrl.shouldShowTipoVisita(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              5.toHeightSpace(),
              const Divider(thickness: 2),
              5.toHeightSpace(),
              const Row(
                children: [
                  LabelWidget(
                    title: "Tipo de Visita ",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  LabelWidget(
                    title: "*",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.red,
                  ),
                ],
              ),
              ...ctrl.visitTypes
                  .where(
                    (element) =>
                        element.idTipoVisita ==
                            ctrl.currentRouteVisit!.tipoVisita ||
                        ctrl.currentRouteVisit!.tipoVisita == 0,
                  )
                  .map(
                    (e) => CustomInkWell(
                      onTap:
                          ctrl.canEdit() && ctrl.isEnabled()
                              ? () {
                                ctrl.setTipoVisita(e.idTipoVisita!);
                              }
                              : null,
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Container(
                          width: MediaQuery.sizeOf(Get.context!).width,
                          color:
                              ctrl.currentRouteVisit!.tipoVisita ==
                                      e.idTipoVisita
                                  ? themesController.getPrimaryColor()
                                  : Colors.white,
                          padding: const EdgeInsets.all(8.0),
                          child: LabelWidget(
                            title: e.descricao ?? "-",
                            fontSize: 15,
                            textColor:
                                ctrl.currentRouteVisit!.tipoVisita ==
                                        e.idTipoVisita
                                    ? Colors.white
                                    : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
            ],
          ),
        ),

        Visibility(
          visible: ctrl.shouldShowImage(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              5.toHeightSpace(),
              const Divider(thickness: 2),
              10.toHeightSpace(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const LabelWidget(
                    title: "Imagens da visita",
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                  ),
                  ButtonWithIcon(
                    onPressed:
                        ctrl.canEdit() && ctrl.isEnabled()
                            ? () async {
                              ctrl.openCameraOption();
                            }
                            : null,
                    fontSize: 12,
                    title: "Adicionar".toUpperCase(),
                    icon: Icon(
                      FontAwesomeIcons.solidImage,
                      size: 14.w,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              Visibility(
                visible:
                    ctrl.currentRouteVisit!.images!.isNotEmpty ||
                    (ctrl.currentRouteVisit!.visitaImagem != null &&
                        ctrl.currentRouteVisit!.visitaImagem!.isNotEmpty),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children:
                          ctrl.currentRouteVisit!.images!.isNotEmpty
                              ? ctrl.currentRouteVisit!.images!
                                  .map(
                                    (e) => SizedBox(
                                      width: 86.w,
                                      child: Column(
                                        children: [
                                          CustomInkWell(
                                            onTap: () {
                                              e.descricaoImagemController.text =
                                                  e.descricao ?? "";
                                              showDialog(
                                                context: Get.context!,
                                                builder:
                                                    (ctx) => AlertDialog(
                                                      title: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          LabelWidget(
                                                            title:
                                                                "Previsualização foto",
                                                            fontSize:
                                                                DeviceSize.fontSize(
                                                                  14,
                                                                  18,
                                                                ),
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                          IconButton(
                                                            onPressed:
                                                                () =>
                                                                    Navigator.pop(
                                                                      ctx,
                                                                    ),
                                                            icon: const Icon(
                                                              Icons.close,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      content: SingleChildScrollView(
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Expanded(
                                                                  child: CustomTextField(
                                                                    readOnly:
                                                                        !(ctrl.canEdit() &&
                                                                            ctrl.isEnabled()),
                                                                    labelText:
                                                                        "Descrição Foto",
                                                                    controller:
                                                                        e.descricaoImagemController,
                                                                  ),
                                                                ),
                                                                ButtonWithIcon(
                                                                  onPressed:
                                                                      (ctrl.canEdit() &&
                                                                              ctrl.isEnabled())
                                                                          ? () => ctrl.setDescricaoImagem(
                                                                            e,
                                                                            e.descricaoImagemController.text,
                                                                          )
                                                                          : null,
                                                                  fontSize: 12,
                                                                  title:
                                                                      "Salvar descrição"
                                                                          .toUpperCase(),
                                                                ),
                                                              ],
                                                            ),
                                                            SizedBox(
                                                              height:
                                                                  MediaQuery.of(
                                                                        ctx,
                                                                      )
                                                                      .size
                                                                      .height *
                                                                  0.7,
                                                              width:
                                                                  MediaQuery.of(
                                                                    ctx,
                                                                  ).size.width *
                                                                  0.7,
                                                              child: PhotoViewPreview(
                                                                imagePath:
                                                                    e.caminhoCompleto!,
                                                                orientation:
                                                                    e.orientation ??
                                                                    Orientation
                                                                        .portrait,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                              );
                                            },
                                            child: SizedBox(
                                              width: 72.w,
                                              child: Stack(
                                                children: [
                                                  Image.file(
                                                    File(e.caminhoCompleto!),
                                                    width: 72.w,
                                                  ),
                                                  SizedBox(
                                                    width: 72.w,
                                                    height: 100.w,
                                                    child: Column(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          size: 30.w,
                                                          Icons
                                                              .add_comment_outlined,
                                                          color: Colors.white,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Visibility(
                                            visible:
                                                ctrl.canEdit() &&
                                                ctrl.isEnabled(),
                                            child: 2.toHeightSpace(),
                                          ),
                                          Visibility(
                                            visible:
                                                ctrl.canEdit() &&
                                                ctrl.isEnabled(),
                                            child: IconButtonWidget(
                                              colorButton: Colors.red,
                                              width: 72.w,
                                              icon: const Icon(
                                                FontAwesomeIcons.trash,
                                                color: Colors.white,
                                                size: 16,
                                              ),
                                              onTap: () async {
                                                await ctrl.removePicture(
                                                  e.caminhoCompleto!,
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                  .toList()
                              : ctrl.currentRouteVisit!.visitaImagem!
                                  .map(
                                    (e) => CustomInkWell(
                                      onTap: () {
                                        e.descricaoImagemController.text =
                                            e.descricao ?? "";
                                        showDialog(
                                          context: Get.context!,
                                          builder:
                                              (ctx) => AlertDialog(
                                                title: Padding(
                                                  padding: EdgeInsets.only(
                                                    left: 10.w,
                                                    right: 10.w,
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      LabelWidget(
                                                        title:
                                                            "Previsualização foto",
                                                        fontSize:
                                                            DeviceSize.fontSize(
                                                              14,
                                                              18,
                                                            ),
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                      IconButton(
                                                        onPressed:
                                                            () => Navigator.pop(
                                                              ctx,
                                                            ),
                                                        icon: const Icon(
                                                          Icons.close,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                content: SingleChildScrollView(
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        children: [
                                                          Expanded(
                                                            child: CustomTextField(
                                                              readOnly: true,
                                                              labelText:
                                                                  "Descrição Foto",
                                                              controller:
                                                                  e.descricaoImagemController,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height:
                                                            MediaQuery.of(
                                                              ctx,
                                                            ).size.height *
                                                            0.7,
                                                        width:
                                                            MediaQuery.of(
                                                              ctx,
                                                            ).size.width *
                                                            0.7,
                                                        child: CachedNetworkImage(
                                                          fit: BoxFit.cover,
                                                          imageUrl:
                                                              e.caminhoCompleto ??
                                                              "",
                                                          placeholder:
                                                              (
                                                                context,
                                                                url,
                                                              ) => SizedBox(
                                                                height: 80.h,
                                                                width: 2.w,
                                                                child:
                                                                    const CircularProgressIndicator.adaptive(),
                                                              ),
                                                          errorWidget:
                                                              (
                                                                context,
                                                                url,
                                                                error,
                                                              ) => SizedBox(
                                                                width: 86.w,
                                                                child: const Icon(
                                                                  Icons
                                                                      .report_problem,
                                                                ),
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                        );
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                          right: 10,
                                        ),
                                        child: SizedBox(
                                          width: 86.w,
                                          child: Stack(
                                            children: [
                                              CachedNetworkImage(
                                                width: 86.w,
                                                imageUrl:
                                                    e.caminhoCompleto ?? "",
                                                placeholder:
                                                    (context, url) => SizedBox(
                                                      height: 80.h,
                                                      width: 2.w,
                                                      child:
                                                          const CircularProgressIndicator.adaptive(),
                                                    ),
                                                errorWidget:
                                                    (context, url, error) =>
                                                        SizedBox(
                                                          width: 86.w,
                                                          child: const Icon(
                                                            Icons
                                                                .report_problem,
                                                          ),
                                                        ),
                                              ),
                                              SizedBox(
                                                width: 80.w,
                                                height: 110.h,
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      size: 30.w,
                                                      Icons.visibility_outlined,
                                                      color: Colors.white,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        10.toHeightSpace(),
        //Carregar Visita acompanhada
        if (ctrl.currentRouteVisit?.visitSelectedList != null &&
            ctrl.currentRouteVisit!.visitSelectedList!.isNotEmpty) ...[
          ...ctrl.currentRouteVisit!.visitSelectedList!.map(
            (e) => VisitInfoColumn(
              ctrl: ctrl,
              model: e,
              index: ctrl.currentRouteVisit!.visitSelectedList!.indexOf(e),
            ),
          ),
          5.toHeightSpace(),
          if (ctrl.currentRouteVisit!.visitSelectedList?.last.name.isNotEmpty ??
              false)
            Visibility(
              visible: ctrl.canEdit() && ctrl.isEnabled(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        width: 2,
                        color: themesController.getPrimaryColor(),
                      ),
                      color: themesController.getPrimaryColor(),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: CustomInkWell(
                      onTap: ctrl.addVisitSelected,
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.add,
                              size: 16,
                              color: themesController.getMenuColor(),
                            ),
                            LabelWidget(
                              title: "Adicionar",
                              fontSize: 10,
                              textColor: themesController.getMenuColor(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          10.toHeightSpace(),
        ],
        5.toHeightSpace(),
        const Divider(thickness: 2),
        10.toHeightSpace(),
        Visibility(
          visible: ctrl.shouldShowCometario(),
          child: CustomTextField(
            labelText: "Comentários *",
            controller: ctrl.comentarioController,
            onChanged: ctrl.setComentarioVisita,
            readOnly: !(ctrl.canEdit() && ctrl.isEnabled()),
            maxLength: 400,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            minLines: 1,
            maxLines: 5,
          ),
        ),
        10.toHeightSpace(),
        Visibility(
          visible: ctrl.shouldShowObjetivosProximaVisita(),
          child: CustomTextField(
            readOnly: !(ctrl.canEdit() && ctrl.isEnabled()),
            labelText: "Objetivos para próxima visita",
            controller: ctrl.objetivosController,
            onChanged: ctrl.setObjetivoVisita,
            maxLength: 400,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            minLines: 1,
            maxLines: 5,
          ),
        ),
      ],
    );
  }
}
