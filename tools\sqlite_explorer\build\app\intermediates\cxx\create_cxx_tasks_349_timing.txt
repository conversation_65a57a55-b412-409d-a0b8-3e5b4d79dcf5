# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 21ms
    create-module-model completed in 32ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    [gap of 16ms]
    create-X86_64-model 10ms
    create-module-model
      [gap of 11ms]
      create-cmake-model 22ms
    create-module-model completed in 38ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 25ms
    [gap of 36ms]
  create-initial-cxx-model completed in 216ms
create_cxx_tasks completed in 223ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 23ms
    create-module-model completed in 32ms
    create-module-model
      create-cmake-model 23ms
    create-module-model completed in 32ms
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 24ms
    [gap of 16ms]
    create-X86-model 12ms
    [gap of 11ms]
  create-initial-cxx-model completed in 192ms
create_cxx_tasks completed in 199ms

