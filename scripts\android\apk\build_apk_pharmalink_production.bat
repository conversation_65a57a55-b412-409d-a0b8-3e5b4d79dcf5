@echo off
echo "Executando flutter clean..."
call flutter clean
if %ERRORLEVEL% neq 0 (
    echo "Erro ao executar flutter clean"
    exit /b %ERRORLEVEL%
)

echo "Executando flutter pub get..."
call flutter pub get
if %ERRORLEVEL% neq 0 (
    echo "Erro ao executar flutter pub get"
    exit /b %ERRORLEVEL%
)

echo "Executando flutter build apk --flavor pharmalink --dart-define=ENV=production..."
call flutter build apk --flavor pharmalink --dart-define=ENV=production
if %ERRORLEVEL% neq 0 (
    echo "Erro ao executar flutter build apk"
    exit /b %ERRORLEVEL%
)

echo "Build concluído com sucesso!"
