import 'dart:convert';
import 'dart:developer';

import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_model.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_request_model.dart';

class OrdersIdealMixController
    extends GetxControllerInstrumentado<OrdersIdealMixController>
    with TraceableController {
  OrdersIdealMixController();
  List<int> distributorIds = [];
  MixIdealModel? dataList;
  List<MixIdealProdutosModel> productCart = [];

  @override
  void onReady() {
    ordersResumeController = Get.find<OrdersResumeController>();

    super.onReady();
  }

  void setDistributorId({List<int>? ids, int? singleId}) {
    distributorIds.clear();
    if (ids != null) {
      distributorIds = ids;
    } else {
      distributorIds.add(singleId!);
    }
  }

  Future<bool> mixIdealIsAvailable() async {
    final result = await ordersIdealMixApi.getIdealMixVerify(
      model: MixIdealRequestModel(
        idLoja: globalParams.getCurrentStore()?.idLoja,
        idsDistribuidores: distributorIds,
      ),
    );
    if (result.data != null) {
      return result.data!.status!;
    } else {
      return false;
    }
  }

  Future<void> getData() async {
    if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync != null) {
      dynamic productSync = jsonDecode(orderPaymentTypeController
          .offlineData!.synchronizationIdealMixResult!);
      final mixIdealData = MixIdealModel.fromJson(productSync);
      dataList = mixIdealData;

      Get.toNamed(RoutesPath.ordersIdealMix);
    } else {
      final loading = PlkLoading();
      loading.show(title: AppStrings.load);
      final result = await ordersIdealMixApi.getIdealMix(
        model: MixIdealRequestModel(
          idLoja: globalParams.getCurrentStore()?.idLoja,
          idsDistribuidores: distributorIds,
        ),
      );
      loading.hide();
      result.when(
        sucess: (data) async {
          dataList = data;

          Get.toNamed(RoutesPath.ordersIdealMix);
        },
        error: (error) {
          SnackbarCustom.snackbarError(error.error);
        },
      );
    }
  }

  void refreshQtdyMix(
    MixIdealCondicoesModel mixIdealCondicoes,
    MixIdealProdutosMixModel? familyMix,
    MixIdealProdutosCondicaoModel? familyConditionalMix,
  ) {
    mixIdealCondicoes.qtdySelected = 0;
    mixIdealCondicoes.qtdyCondicaoSelected = 0;
    if (mixIdealCondicoes.produtosMix != null) {
      for (var mix in mixIdealCondicoes.produtosMix!) {
        if (mix.produtos != null) {
          for (var produto in mix.produtos!) {
            if (produto.qtdySelected != null) {
              mixIdealCondicoes.qtdySelected =
                  (mixIdealCondicoes.qtdySelected ?? 0) + produto.qtdySelected!;
            }
          }
        }
      }
      for (var mix in mixIdealCondicoes.produtosCondicao!) {
        if (mix.produtos != null) {
          for (var produto in mix.produtos!) {
            if (produto.qtdySelected != null) {
              mixIdealCondicoes.qtdyCondicaoSelected =
                  (mixIdealCondicoes.qtdyCondicaoSelected ?? 0) +
                      produto.qtdySelected!;
            }
          }
        }
      }
    }
    if (familyMix != null) {
      familyMix.qtdySelected = 0;
      if (familyMix.produtos != null) {
        for (var produto in familyMix.produtos!) {
          familyMix.qtdySelected =
              (familyMix.qtdySelected ?? 0) + produto.qtdySelected!;
        }
      }
      print(familyMix.qtdySelected.toString());
    }
    if (familyConditionalMix != null) {
      familyConditionalMix.qtdySelected = 0;
      if (familyConditionalMix.produtos != null) {
        for (var produto in familyConditionalMix.produtos!) {
          familyConditionalMix.qtdySelected =
              (familyConditionalMix.qtdySelected ?? 0) + produto.qtdySelected!;
        }
      }
      print(familyConditionalMix.qtdySelected.toString());
    }
  }

  void setQtdyDown(
    MixIdealProdutosModel item,
    int? minQtdy,
    String nameFamily,
    MixIdealCondicoesModel mixIdealCondicoes,
    MixIdealProdutosMixModel? familyMix,
    MixIdealProdutosCondicaoModel? familyConditionalMix,
  ) {
    item.qtdySelected = item.qtdySelected! - 1;
    if (item.qtdySelected! < 0) item.qtdySelected = 0;

    refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);

    update();
  }

  Future<void> setQtdyUp(
      MixIdealProdutosModel item,
      int? minQtdy,
      String nameFamily,
      MixIdealCondicoesModel mixIdealCondicoes,
      MixIdealProdutosMixModel? familyMix,
      MixIdealProdutosCondicaoModel? familyConditionalMix) async {
    int counter = 1;
    item.qtdySelected = item.qtdySelected! + counter;

    refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
    if (mixIdealCondicoes.possuiQuantidadeMaximaMix == true) {
      if (mixIdealCondicoes.qtdySelected! >
          mixIdealCondicoes.quantidadeMaximaMix!) {
        item.qtdySelected = item.qtdySelected! - counter;
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      }
    } else if (mixIdealCondicoes.possuiQuantidadeMaximaCondicao == true) {
      if (mixIdealCondicoes.qtdyCondicaoSelected! >
          mixIdealCondicoes.quantidadeMaximaCondicao!) {
        item.qtdySelected = item.qtdySelected! - counter;
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            await showMessageErrorMix();
            refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
          }
        }
      }
    } else if (familyMix != null) {
      if (familyMix.quantidadeMaxima != null) {
        if (familyMix.qtdySelected! > familyMix.quantidadeMaxima!) {
          item.qtdySelected = item.qtdySelected! - counter;
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            await showMessageErrorMix();
          }
        }
      }
    } else if (familyConditionalMix != null) {
      if (familyConditionalMix.quantidadeMaxima != null) {
        if (familyConditionalMix.qtdySelected! >
            familyConditionalMix.quantidadeMaxima!) {
          item.qtdySelected = item.qtdySelected! - counter;
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            await showMessageErrorMix();
          }
        }
      }
    }

    update();
  }

  Future<void> setQtdyEdit(
      MixIdealProdutosModel item,
      int newQty,
      int? minQtdy,
      String nameFamily,
      MixIdealCondicoesModel mixIdealCondicoes,
      MixIdealProdutosMixModel? familyMix,
      MixIdealProdutosCondicaoModel? familyConditionalMix) async {
    int oldQtdySelected = item.qtdySelected ?? 0;
    int oldQtdyCondicaoSelected = mixIdealCondicoes.qtdyCondicaoSelected ?? 0;
    int oldQtdyMixSelected = mixIdealCondicoes.qtdySelected ?? 0;
    log('teste: oldQtdyCondicaoSelected: $oldQtdyCondicaoSelected');
    item.qtdySelected = newQty;

    refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
    if (mixIdealCondicoes.possuiQuantidadeMaximaMix == true) {
      if (mixIdealCondicoes.qtdySelected! >
          mixIdealCondicoes.quantidadeMaximaMix!) {
        if (mixIdealCondicoes.quantidadeMaximaMix! > oldQtdyMixSelected) {
          item.qtdySelected =
              mixIdealCondicoes.quantidadeMaximaMix! - oldQtdyMixSelected;
        } else {
          item.qtdySelected = oldQtdySelected;
        }

        item.qtdyController!.text = item.qtdySelected!.toString();
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      }
    } else if (mixIdealCondicoes.possuiQuantidadeMaximaCondicao == true) {
      if (mixIdealCondicoes.qtdyCondicaoSelected! >
          mixIdealCondicoes.quantidadeMaximaCondicao!) {
        if (mixIdealCondicoes.quantidadeMaximaCondicao! >
            oldQtdyCondicaoSelected) {
          item.qtdySelected = mixIdealCondicoes.quantidadeMaximaCondicao! -
              oldQtdyCondicaoSelected;
        } else {
          item.qtdySelected = oldQtdySelected;
        }
        item.qtdyController!.text = item.qtdySelected!.toString();
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      }
    } else if (familyMix != null) {
      if (familyMix.quantidadeMaxima != null) {
        if (familyMix.qtdySelected! > familyMix.quantidadeMaxima!) {
          item.qtdySelected = oldQtdySelected;
          item.qtdyController!.text = item.qtdySelected!.toString();
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            item.qtdyController!.text = item.qtdySelected!.toString();
            await showMessageErrorMix();
          }
        }
      }
    } else if (familyConditionalMix != null) {
      if (familyConditionalMix.quantidadeMaxima != null) {
        if (familyConditionalMix.qtdySelected! >
            familyConditionalMix.quantidadeMaxima!) {
          item.qtdySelected = oldQtdySelected;
          item.qtdyController!.text = item.qtdySelected!.toString();
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            item.qtdyController!.text = item.qtdySelected!.toString();
            await showMessageErrorMix();
          }
        }
      }
    }

    update();
  }

  Future<void> showMessageErrorMix() async {
    if (Get.isSnackbarOpen) {
      await Get.closeCurrentSnackbar();
    }
    SnackbarCustom.snackbarError(
        "A quantidade que você digitou é superior ao limite permitido para este item/família. Ajustamos a quantidade para a máxima permitida");
  }

  Future<void> showMessageErrorMixLimit() async {
    if (Get.isSnackbarOpen) {
      await Get.closeCurrentSnackbar();
    }
    SnackbarCustom.snackbarError(
        'Esta oferta possui limite máximo de itens obrigatórios, a soma dos itens solicitados até agora ultrapassou o limite, por pavor, ajuste a quantidade você digitou para seguir');
  }

  Future<void> advance() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");

    final loading = PlkLoading();
    loading.show(title: AppStrings.mixIdealValidate);

    String errorMessage = "";
    bool hasValidationError = false;
    List<String> mixIdealErrors = [];
    List<String> familiaErrors = [];
    List<String> produtoErrors = [];
    List<String> condicaoErrors = [];
    try {
      for (var c in dataList!.condicoes!) {
        for (var pm in c.produtosMix!) {
          for (var p in pm.produtos!.where((p) => p.qtdySelected! > 0)) {
            // Validação para possuiQuantidadeMaximaMix
            if (c.possuiQuantidadeMaximaMix == true) {
              if (c.qtdySelected! > c.quantidadeMaximaMix!) {
                String errorItem =
                    "${p.descricaoProduto ?? 'Produto'} - Qtd: ${p.qtdySelected} - Max Mix: ${c.quantidadeMaximaMix}";
                if (!mixIdealErrors.contains(errorItem)) {
                  mixIdealErrors.add(errorItem);
                }
              }
            } else {
              // Validação para família (pm)
              if (pm.quantidadeMinima != null && pm.quantidadeMaxima != null) {
                if (pm.qtdySelected! < pm.quantidadeMinima! ||
                    pm.qtdySelected! > pm.quantidadeMaxima!) {
                  String errorItem =
                      "${pm.descricaoFamilia ?? 'Família'} - Qtd: ${pm.qtdySelected} - Min: ${pm.quantidadeMinima} - Max: ${pm.quantidadeMaxima}";
                  if (!familiaErrors.contains(errorItem)) {
                    familiaErrors.add(errorItem);
                  }
                }
              } else {
                // Validação para produto (p)
                if (p.quantidadeMinima != null && p.quantidadeMaxima != null) {
                  if (p.qtdySelected! < p.quantidadeMinima! ||
                      p.qtdySelected! > p.quantidadeMaxima!) {
                    String errorItem =
                        "${p.descricaoProduto ?? 'Produto'} - Qtd: ${p.qtdySelected} - Min: ${p.quantidadeMinima} - Max: ${p.quantidadeMaxima}";
                    if (!produtoErrors.contains(errorItem)) {
                      produtoErrors.add(errorItem);
                    }
                  }
                }
              }
            }

            // Validação para possuiQuantidadeMaximaCondicao
            if (c.possuiQuantidadeMaximaCondicao == true) {
              if (c.qtdySelected! > c.quantidadeMaximaCondicao!) {
                String errorItem =
                    "${c.descricao ?? 'Condição'} - Qtd: ${c.qtdySelected} - Max: ${c.quantidadeMaximaCondicao}";
                if (!condicaoErrors.contains(errorItem)) {
                  condicaoErrors.add(errorItem);
                }
              }
            }
          }
        }
      }

// Compilar todas as mensagens de erro
      String errorMessage = "";

      if (mixIdealErrors.isNotEmpty) {
        errorMessage += "Mix Ideal:\n- ";
        errorMessage += mixIdealErrors.join("\n\n- ");
      }

      if (familiaErrors.isNotEmpty) {
        if (errorMessage.isNotEmpty) errorMessage += "\n\n";
        errorMessage += "Família:\n- ";
        errorMessage += familiaErrors.join("\n\n- ");
      }

      if (produtoErrors.isNotEmpty) {
        if (errorMessage.isNotEmpty) errorMessage += "\n\n";
        errorMessage += "Produto:\n- ";
        errorMessage += produtoErrors.join("\n\n- ");
      }

      if (condicaoErrors.isNotEmpty) {
        if (errorMessage.isNotEmpty) errorMessage += "\n\n";
        errorMessage += "Condição:\n- ";
        errorMessage += condicaoErrors.join("\n\n- ");
      }

// Se existe algum erro, define hasValidationError como true
      hasValidationError = mixIdealErrors.isNotEmpty ||
          familiaErrors.isNotEmpty ||
          produtoErrors.isNotEmpty ||
          condicaoErrors.isNotEmpty;

      loading.hide();
      if (hasValidationError) {
        await Dialogs.confirm(
          AppStrings.attention,
          AppStrings.mixIdealQtyMessage(errorMessage),
          onPressedOk: () async {
            GetC.close();
            await saveCart();
          },
          buttonNameCancel: "Corrigir".toUpperCase(),
          buttonNameOk: "Sim".toUpperCase(),
        );
        update();
        return;
      } else {
        await saveCart();
      }
    } catch (e, s) {
      loading.hide();
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> saveCart() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("saveCart");

    final loading = PlkLoading();
    try {
      loading.show(title: AppStrings.validMixIdeal);

      final listMix = getProdutosValidos();

      await ordersController.mixIdealRefreshCart(listMix);
      subAction.reportValue("listMix", listMix);

      loading.hide();
      GetC.close();
    } catch (e, s) {
      loading.hide();
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  bool isFamilyMixShowLimit(MixIdealProdutosMixModel item) {
    return item.quantidadeMinima != null ||
        item.quantidadeMaxima != null ||
        item.desconto != null;
  }

  List<MixIdealProdutosModel> getProdutosValidos() {
    List<MixIdealProdutosModel> produtosValidos = [];

    // Se dataList for nulo ou condicoes for nulo, retorna lista vazia
    if (dataList == null || dataList!.condicoes == null) {
      return produtosValidos;
    }

    for (var c in dataList!.condicoes!) {
      for (var pm in c.produtosMix!) {
        for (var p in pm.produtos!.where((p) => p.qtdySelected! > 0)) {
          bool produtoValido = true;

          // Validação para possuiQuantidadeMaximaMix
          if (c.possuiQuantidadeMaximaMix == true) {
            if (c.qtdySelected! > c.quantidadeMaximaMix!) {
              produtoValido = false;
            }
          } else {
            // Validação para família (pm)
            if (pm.quantidadeMinima != null && pm.quantidadeMaxima != null) {
              if (pm.qtdySelected! < pm.quantidadeMinima! ||
                  pm.qtdySelected! > pm.quantidadeMaxima!) {
                produtoValido = false;
              }
            } else {
              // Validação para produto (p)
              if (p.quantidadeMinima != null && p.quantidadeMaxima != null) {
                if (p.qtdySelected! < p.quantidadeMinima! ||
                    p.qtdySelected! > p.quantidadeMaxima!) {
                  produtoValido = false;
                }
              }
            }
          }

          // Validação para possuiQuantidadeMaximaCondicao
          if (c.possuiQuantidadeMaximaCondicao == true) {
            if (c.qtdySelected! > c.quantidadeMaximaCondicao!) {
              produtoValido = false;
            }
          }

          // Se o produto passou em todas as validações, adiciona à lista
          if (produtoValido) {
            produtosValidos.add(p);
          }
        }
      }
    }

    return produtosValidos;
  }
}
